import React, { useState, useEffect } from 'react';
import { X, Search, Users, User, Plus } from 'lucide-react';
import { useQuery } from '@tanstack/react-query';
import { chatApi } from '../../api/chatApi';
import { useFirebaseAuth } from '../../hooks/useFirebaseAuth';
import { useNavigate } from 'react-router-dom';
import toast from 'react-hot-toast';
import LoadingSpinner from '../common/LoadingSpinner';

const NewChatModal = ({ isOpen, onClose }) => {
  const [searchQuery, setSearchQuery] = useState('');
  const [selectedUsers, setSelectedUsers] = useState([]);
  const [isCreatingChat, setIsCreatingChat] = useState(false);
  const [chatType, setChatType] = useState('direct'); // 'direct' or 'group'
  const [groupName, setGroupName] = useState('');
  const { user } = useFirebaseAuth();
  const navigate = useNavigate();

  // Search users
  const {
    data: searchResults,
    isLoading: isSearching,
    error: searchError
  } = useQuery({
    queryKey: ['searchUsers', searchQuery],
    queryFn: () => chatApi.searchUsers(searchQuery),
    enabled: searchQuery.length >= 2,
    staleTime: 30 * 1000, // 30 seconds
  });

  const users = searchResults?.users || [];

  // Reset state when modal opens/closes
  useEffect(() => {
    if (isOpen) {
      setSearchQuery('');
      setSelectedUsers([]);
      setChatType('direct');
      setGroupName('');
    }
  }, [isOpen]);

  if (!isOpen) return null;

  const handleUserSelect = (selectedUser) => {
    if (chatType === 'direct') {
      // For direct chat, immediately create chat with this user
      createDirectChat(selectedUser);
    } else {
      // For group chat, add to selected users
      if (selectedUsers.find(u => u._id === selectedUser._id)) {
        setSelectedUsers(prev => prev.filter(u => u._id !== selectedUser._id));
      } else {
        setSelectedUsers(prev => [...prev, selectedUser]);
      }
    }
  };

  const createDirectChat = async (otherUser) => {
    try {
      setIsCreatingChat(true);
      
      const chatData = {
        type: 'direct',
        members: [otherUser._id]
      };

      const response = await chatApi.createChat(chatData);
      
      toast.success(`Chat created with ${otherUser.name}`);
      navigate(`/chat/${response.chat._id}`);
      onClose();
    } catch (error) {
      console.error('Failed to create direct chat:', error);
      toast.error('Failed to create chat');
    } finally {
      setIsCreatingChat(false);
    }
  };

  const createGroupChat = async () => {
    if (!groupName.trim()) {
      toast.error('Please enter a group name');
      return;
    }

    if (selectedUsers.length < 2) {
      toast.error('Please select at least 2 members for a group');
      return;
    }

    try {
      setIsCreatingChat(true);
      
      const chatData = {
        type: 'group',
        groupName: groupName.trim(),
        members: selectedUsers.map(u => u._id)
      };

      const response = await chatApi.createChat(chatData);
      
      toast.success(`Group "${groupName}" created successfully`);
      navigate(`/chat/${response.chat._id}`);
      onClose();
    } catch (error) {
      console.error('Failed to create group chat:', error);
      toast.error('Failed to create group');
    } finally {
      setIsCreatingChat(false);
    }
  };

  const handleSubmit = (e) => {
    e.preventDefault();
    if (chatType === 'group') {
      createGroupChat();
    }
  };

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div className="bg-whatsapp-message-bg rounded-lg w-full max-w-md mx-4 max-h-[80vh] flex flex-col">
        {/* Header */}
        <div className="flex items-center justify-between p-6 border-b border-whatsapp-dark">
          <h2 className="text-xl font-semibold text-white">New Chat</h2>
          <button
            onClick={onClose}
            className="p-2 text-whatsapp-gray hover:text-white transition-colors"
            title="Close"
          >
            <X className="w-5 h-5" />
          </button>
        </div>

        {/* Chat Type Selector */}
        <div className="p-4 border-b border-whatsapp-dark">
          <div className="flex space-x-2">
            <button
              onClick={() => setChatType('direct')}
              className={`flex-1 flex items-center justify-center space-x-2 py-2 px-4 rounded-lg transition-colors ${
                chatType === 'direct'
                  ? 'bg-whatsapp-primary text-white'
                  : 'bg-whatsapp-dark text-whatsapp-gray hover:text-white'
              }`}
            >
              <User className="w-4 h-4" />
              <span>Direct Chat</span>
            </button>
            <button
              onClick={() => setChatType('group')}
              className={`flex-1 flex items-center justify-center space-x-2 py-2 px-4 rounded-lg transition-colors ${
                chatType === 'group'
                  ? 'bg-whatsapp-primary text-white'
                  : 'bg-whatsapp-dark text-whatsapp-gray hover:text-white'
              }`}
            >
              <Users className="w-4 h-4" />
              <span>Group Chat</span>
            </button>
          </div>
        </div>

        {/* Group Name Input (for group chats) */}
        {chatType === 'group' && (
          <div className="p-4 border-b border-whatsapp-dark">
            <input
              type="text"
              value={groupName}
              onChange={(e) => setGroupName(e.target.value)}
              placeholder="Enter group name..."
              className="w-full bg-whatsapp-dark text-white rounded-lg px-3 py-2 input-focus"
            />
          </div>
        )}

        {/* Search Input */}
        <div className="p-4 border-b border-whatsapp-dark">
          <div className="relative">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-whatsapp-gray w-4 h-4" />
            <input
              type="text"
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              placeholder="Search users..."
              className="w-full bg-whatsapp-dark text-white rounded-lg pl-10 pr-3 py-2 input-focus"
            />
          </div>
        </div>

        {/* Selected Users (for group chats) */}
        {chatType === 'group' && selectedUsers.length > 0 && (
          <div className="p-4 border-b border-whatsapp-dark">
            <p className="text-whatsapp-gray text-sm mb-2">
              Selected members ({selectedUsers.length}):
            </p>
            <div className="flex flex-wrap gap-2">
              {selectedUsers.map(user => (
                <div
                  key={user._id}
                  className="flex items-center space-x-2 bg-whatsapp-primary rounded-full px-3 py-1"
                >
                  <img
                    src={user.avatar || `https://ui-avatars.com/api/?name=${encodeURIComponent(user.name)}&background=00a884&color=fff&size=24`}
                    alt={user.name}
                    className="w-5 h-5 rounded-full"
                  />
                  <span className="text-white text-sm">{user.name}</span>
                  <button
                    onClick={() => handleUserSelect(user)}
                    className="text-white hover:text-gray-300"
                  >
                    <X className="w-3 h-3" />
                  </button>
                </div>
              ))}
            </div>
          </div>
        )}

        {/* User List */}
        <div className="flex-1 overflow-y-auto">
          {isSearching ? (
            <div className="flex items-center justify-center py-8">
              <LoadingSpinner size="md" />
            </div>
          ) : searchError ? (
            <div className="text-center py-8 text-red-400">
              Failed to search users
            </div>
          ) : searchQuery.length < 2 ? (
            <div className="text-center py-8 text-whatsapp-gray">
              Type at least 2 characters to search for users
            </div>
          ) : users.length === 0 ? (
            <div className="text-center py-8 text-whatsapp-gray">
              No users found
            </div>
          ) : (
            <div className="p-4 space-y-2">
              {users
                .filter(u => u._id !== user?._id) // Exclude current user
                .map(searchUser => {
                  const isSelected = selectedUsers.find(u => u._id === searchUser._id);
                  
                  return (
                    <button
                      key={searchUser._id}
                      onClick={() => handleUserSelect(searchUser)}
                      disabled={isCreatingChat}
                      className={`w-full flex items-center space-x-3 p-3 rounded-lg transition-colors ${
                        isSelected
                          ? 'bg-whatsapp-primary'
                          : 'hover:bg-whatsapp-dark'
                      } ${isCreatingChat ? 'opacity-50 cursor-not-allowed' : ''}`}
                    >
                      <img
                        src={searchUser.avatar || `https://ui-avatars.com/api/?name=${encodeURIComponent(searchUser.name)}&background=00a884&color=fff&size=40`}
                        alt={searchUser.name}
                        className="w-10 h-10 rounded-full object-cover"
                      />
                      <div className="flex-1 text-left">
                        <p className="text-white font-medium">{searchUser.name}</p>
                        <p className="text-whatsapp-gray text-sm">{searchUser.email}</p>
                      </div>
                      {chatType === 'group' && isSelected && (
                        <div className="text-white">
                          <X className="w-4 h-4" />
                        </div>
                      )}
                    </button>
                  );
                })}
            </div>
          )}
        </div>

        {/* Footer (for group chats) */}
        {chatType === 'group' && selectedUsers.length > 0 && (
          <div className="p-4 border-t border-whatsapp-dark">
            <button
              onClick={handleSubmit}
              disabled={isCreatingChat || !groupName.trim() || selectedUsers.length < 2}
              className="w-full btn-primary disabled:opacity-50 disabled:cursor-not-allowed"
            >
              {isCreatingChat ? (
                <div className="flex items-center justify-center space-x-2">
                  <LoadingSpinner size="sm" />
                  <span>Creating Group...</span>
                </div>
              ) : (
                <div className="flex items-center justify-center space-x-2">
                  <Plus className="w-4 h-4" />
                  <span>Create Group ({selectedUsers.length} members)</span>
                </div>
              )}
            </button>
          </div>
        )}
      </div>
    </div>
  );
};

export default NewChatModal;
