const { verifyIdToken } = require('../config/firebase');
const User = require('../models/User');

// Socket.io authentication middleware
const socketAuthMiddleware = async (socket, next) => {
  try {
    console.log(`🔐 Authenticating socket: ${socket.id}`);

    const token = socket.handshake.auth.token || socket.handshake.headers.authorization;

    if (!token) {
      console.error('❌ No token provided for socket:', socket.id);
      return next(new Error('Authentication error: No token provided'));
    }

    // Remove 'Bearer ' prefix if present
    const cleanToken = token.startsWith('Bearer ') ? token.substring(7) : token;

    // Verify the Firebase ID token
    const decodedToken = await verifyIdToken(cleanToken);

    // Find user in our database
    const user = await User.findOne({ firebaseUid: decodedToken.uid });

    if (!user) {
      console.error('❌ User not found in database for UID:', decodedToken.uid);
      return next(new Error('Authentication error: User not found'));
    }

    // Attach user info to socket
    socket.user = user;
    socket.firebaseUser = decodedToken;

    console.log(`✅ Socket authenticated for user: ${user.name} (${user.email})`);
    next();
  } catch (error) {
    console.error('❌ Socket authentication error:', {
      socketId: socket.id,
      error: error.message,
      stack: error.stack
    });
    next(new Error('Authentication error: Invalid token'));
  }
};

module.exports = socketAuthMiddleware;
