const mongoose = require('mongoose');

const userSchema = new mongoose.Schema({
  firebaseUid: {
    type: String,
    required: true,
    unique: true
  },
  email: {
    type: String,
    required: true,
    unique: true,
    lowercase: true
  },
  name: {
    type: String,
    required: true,
    trim: true
  },
  avatar: {
    type: String,
    default: ''
  },
  isOnline: {
    type: Boolean,
    default: false
  },
  lastSeen: {
    type: Date,
    default: Date.now
  },
  socketId: {
    type: String,
    default: null
  }
}, {
  timestamps: true
});

// Index for efficient queries
userSchema.index({ isOnline: 1 });

// Virtual for user's active status
userSchema.virtual('isActive').get(function() {
  if (this.isOnline) return true;
  if (!this.lastSeen) return false;
  return (Date.now() - this.lastSeen.getTime()) < 5 * 60 * 1000; // 5 minutes
});

// Ensure virtual fields are serialized
userSchema.set('toJSON', { virtuals: true });

module.exports = mongoose.model('User', userSchema);
