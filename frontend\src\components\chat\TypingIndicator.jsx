import React from 'react';

const TypingIndicator = ({ userNames = [] }) => {
  if (!userNames || userNames.length === 0) {
    return null;
  }

  const getTypingText = () => {
    if (userNames.length === 1) {
      return `${userNames[0]} is typing...`;
    } else if (userNames.length === 2) {
      return `${userNames[0]} and ${userNames[1]} are typing...`;
    } else {
      return `${userNames[0]} and ${userNames.length - 1} others are typing...`;
    }
  };

  return (
    <div className="flex items-center space-x-2 text-whatsapp-gray text-sm">
      <span>{getTypingText()}</span>
      <div className="typing-dots">
        <div className="typing-dot"></div>
        <div className="typing-dot"></div>
        <div className="typing-dot"></div>
      </div>
    </div>
  );
};

export default TypingIndicator;
