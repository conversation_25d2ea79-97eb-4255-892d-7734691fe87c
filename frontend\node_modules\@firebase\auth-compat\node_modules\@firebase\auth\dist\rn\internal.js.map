{"version": 3, "file": "internal.js", "sources": ["../../src/platform_browser/persistence/browser.ts", "../../src/platform_browser/persistence/local_storage.ts", "../../src/platform_browser/persistence/session_storage.ts", "../../src/platform_browser/messagechannel/promise.ts", "../../src/platform_browser/messagechannel/receiver.ts", "../../src/core/util/event_id.ts", "../../src/platform_browser/messagechannel/sender.ts", "../../src/platform_browser/persistence/indexed_db.ts", "../../src/core/util/resolver.ts", "../../src/core/strategies/idp.ts", "../../src/core/strategies/abstract_popup_redirect_operation.ts", "../../src/platform_browser/strategies/popup.ts", "../../src/core/strategies/redirect.ts", "../../src/platform_browser/strategies/redirect.ts", "../../src/core/auth/auth_event_manager.ts", "../../src/api/project_config/get_project_config.ts", "../../src/core/util/validate_origin.ts", "../../src/platform_browser/iframe/gapi.ts", "../../src/platform_browser/iframe/iframe.ts", "../../src/platform_browser/util/popup.ts", "../../src/core/util/handler.ts", "../../src/platform_browser/popup_redirect.ts", "../../src/platform_browser/index.ts", "../../src/platform_cordova/plugins.ts", "../../src/platform_cordova/popup_redirect/utils.ts", "../../src/platform_cordova/popup_redirect/events.ts", "../../src/platform_cordova/popup_redirect/popup_redirect.ts", "../../internal/index.ts"], "sourcesContent": ["/**\n * @license\n * Copyright 2019 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport {\n  PersistenceValue,\n  STORAGE_AVAILABLE_KEY,\n  PersistenceType\n} from '../../core/persistence';\n\n// There are two different browser persistence types: local and session.\n// Both have the same implementation but use a different underlying storage\n// object.\n\nexport abstract class BrowserPersistenceClass {\n  protected constructor(\n    protected readonly storageRetriever: () => Storage,\n    readonly type: PersistenceType\n  ) {}\n\n  _isAvailable(): Promise<boolean> {\n    try {\n      if (!this.storage) {\n        return Promise.resolve(false);\n      }\n      this.storage.setItem(STORAGE_AVAILABLE_KEY, '1');\n      this.storage.removeItem(STORAGE_AVAILABLE_KEY);\n      return Promise.resolve(true);\n    } catch {\n      return Promise.resolve(false);\n    }\n  }\n\n  _set(key: string, value: PersistenceValue): Promise<void> {\n    this.storage.setItem(key, JSON.stringify(value));\n    return Promise.resolve();\n  }\n\n  _get<T extends PersistenceValue>(key: string): Promise<T | null> {\n    const json = this.storage.getItem(key);\n    return Promise.resolve(json ? JSON.parse(json) : null);\n  }\n\n  _remove(key: string): Promise<void> {\n    this.storage.removeItem(key);\n    return Promise.resolve();\n  }\n\n  protected get storage(): Storage {\n    return this.storageRetriever();\n  }\n}\n", "/**\n * @license\n * Copyright 2020 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { Persistence } from '../../model/public_types';\n\nimport { _isMobileBrowser, _isIE10 } from '../../core/util/browser';\nimport {\n  PersistenceInternal as InternalPersistence,\n  PersistenceType,\n  PersistenceValue,\n  StorageEventListener\n} from '../../core/persistence';\nimport { BrowserPersistenceClass } from './browser';\n\n// The polling period in case events are not supported\nexport const _POLLING_INTERVAL_MS = 1000;\n\n// The IE 10 localStorage cross tab synchronization delay in milliseconds\nconst IE10_LOCAL_STORAGE_SYNC_DELAY = 10;\n\nclass BrowserLocalPersistence\n  extends BrowserPersistenceClass\n  implements InternalPersistence\n{\n  static type: 'LOCAL' = 'LOCAL';\n\n  constructor() {\n    super(() => window.localStorage, PersistenceType.LOCAL);\n  }\n\n  private readonly boundEventHandler = (\n    event: StorageEvent,\n    poll?: boolean\n  ): void => this.onStorageEvent(event, poll);\n  private readonly listeners: Record<string, Set<StorageEventListener>> = {};\n  private readonly localCache: Record<string, string | null> = {};\n  // setTimeout return value is platform specific\n  // eslint-disable-next-line @typescript-eslint/no-explicit-any\n  private pollTimer: any | null = null;\n\n  // Whether to use polling instead of depending on window events\n  private readonly fallbackToPolling = _isMobileBrowser();\n  readonly _shouldAllowMigration = true;\n\n  private forAllChangedKeys(\n    cb: (key: string, oldValue: string | null, newValue: string | null) => void\n  ): void {\n    // Check all keys with listeners on them.\n    for (const key of Object.keys(this.listeners)) {\n      // Get value from localStorage.\n      const newValue = this.storage.getItem(key);\n      const oldValue = this.localCache[key];\n      // If local map value does not match, trigger listener with storage event.\n      // Differentiate this simulated event from the real storage event.\n      if (newValue !== oldValue) {\n        cb(key, oldValue, newValue);\n      }\n    }\n  }\n\n  private onStorageEvent(event: StorageEvent, poll = false): void {\n    // Key would be null in some situations, like when localStorage is cleared\n    if (!event.key) {\n      this.forAllChangedKeys(\n        (key: string, _oldValue: string | null, newValue: string | null) => {\n          this.notifyListeners(key, newValue);\n        }\n      );\n      return;\n    }\n\n    const key = event.key;\n\n    // Check the mechanism how this event was detected.\n    // The first event will dictate the mechanism to be used.\n    if (poll) {\n      // Environment detects storage changes via polling.\n      // Remove storage event listener to prevent possible event duplication.\n      this.detachListener();\n    } else {\n      // Environment detects storage changes via storage event listener.\n      // Remove polling listener to prevent possible event duplication.\n      this.stopPolling();\n    }\n\n    const triggerListeners = (): void => {\n      // Keep local map up to date in case storage event is triggered before\n      // poll.\n      const storedValue = this.storage.getItem(key);\n      if (!poll && this.localCache[key] === storedValue) {\n        // Real storage event which has already been detected, do nothing.\n        // This seems to trigger in some IE browsers for some reason.\n        return;\n      }\n      this.notifyListeners(key, storedValue);\n    };\n\n    const storedValue = this.storage.getItem(key);\n    if (\n      _isIE10() &&\n      storedValue !== event.newValue &&\n      event.newValue !== event.oldValue\n    ) {\n      // IE 10 has this weird bug where a storage event would trigger with the\n      // correct key, oldValue and newValue but localStorage.getItem(key) does\n      // not yield the updated value until a few milliseconds. This ensures\n      // this recovers from that situation.\n      setTimeout(triggerListeners, IE10_LOCAL_STORAGE_SYNC_DELAY);\n    } else {\n      triggerListeners();\n    }\n  }\n\n  private notifyListeners(key: string, value: string | null): void {\n    this.localCache[key] = value;\n    const listeners = this.listeners[key];\n    if (listeners) {\n      for (const listener of Array.from(listeners)) {\n        listener(value ? JSON.parse(value) : value);\n      }\n    }\n  }\n\n  private startPolling(): void {\n    this.stopPolling();\n\n    this.pollTimer = setInterval(() => {\n      this.forAllChangedKeys(\n        (key: string, oldValue: string | null, newValue: string | null) => {\n          this.onStorageEvent(\n            new StorageEvent('storage', {\n              key,\n              oldValue,\n              newValue\n            }),\n            /* poll */ true\n          );\n        }\n      );\n    }, _POLLING_INTERVAL_MS);\n  }\n\n  private stopPolling(): void {\n    if (this.pollTimer) {\n      clearInterval(this.pollTimer);\n      this.pollTimer = null;\n    }\n  }\n\n  private attachListener(): void {\n    window.addEventListener('storage', this.boundEventHandler);\n  }\n\n  private detachListener(): void {\n    window.removeEventListener('storage', this.boundEventHandler);\n  }\n\n  _addListener(key: string, listener: StorageEventListener): void {\n    if (Object.keys(this.listeners).length === 0) {\n      // Whether browser can detect storage event when it had already been pushed to the background.\n      // This may happen in some mobile browsers. A localStorage change in the foreground window\n      // will not be detected in the background window via the storage event.\n      // This was detected in iOS 7.x mobile browsers\n      if (this.fallbackToPolling) {\n        this.startPolling();\n      } else {\n        this.attachListener();\n      }\n    }\n    if (!this.listeners[key]) {\n      this.listeners[key] = new Set();\n      // Populate the cache to avoid spuriously triggering on first poll.\n      this.localCache[key] = this.storage.getItem(key);\n    }\n    this.listeners[key].add(listener);\n  }\n\n  _removeListener(key: string, listener: StorageEventListener): void {\n    if (this.listeners[key]) {\n      this.listeners[key].delete(listener);\n\n      if (this.listeners[key].size === 0) {\n        delete this.listeners[key];\n      }\n    }\n\n    if (Object.keys(this.listeners).length === 0) {\n      this.detachListener();\n      this.stopPolling();\n    }\n  }\n\n  // Update local cache on base operations:\n\n  async _set(key: string, value: PersistenceValue): Promise<void> {\n    await super._set(key, value);\n    this.localCache[key] = JSON.stringify(value);\n  }\n\n  async _get<T extends PersistenceValue>(key: string): Promise<T | null> {\n    const value = await super._get<T>(key);\n    this.localCache[key] = JSON.stringify(value);\n    return value;\n  }\n\n  async _remove(key: string): Promise<void> {\n    await super._remove(key);\n    delete this.localCache[key];\n  }\n}\n\n/**\n * An implementation of {@link Persistence} of type `LOCAL` using `localStorage`\n * for the underlying storage.\n *\n * @public\n */\nexport const browserLocalPersistence: Persistence = BrowserLocalPersistence;\n", "/**\n * @license\n * Copyright 2020 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { Persistence } from '../../model/public_types';\n\nimport {\n  PersistenceInternal as InternalPersistence,\n  PersistenceType,\n  StorageEventListener\n} from '../../core/persistence';\nimport { BrowserPersistenceClass } from './browser';\n\nclass BrowserSessionPersistence\n  extends BrowserPersistenceClass\n  implements InternalPersistence\n{\n  static type: 'SESSION' = 'SESSION';\n\n  constructor() {\n    super(() => window.sessionStorage, PersistenceType.SESSION);\n  }\n\n  _addListener(_key: string, _listener: StorageEventListener): void {\n    // Listeners are not supported for session storage since it cannot be shared across windows\n    return;\n  }\n\n  _removeListener(_key: string, _listener: StorageEventListener): void {\n    // Listeners are not supported for session storage since it cannot be shared across windows\n    return;\n  }\n}\n\n/**\n * An implementation of {@link Persistence} of `SESSION` using `sessionStorage`\n * for the underlying storage.\n *\n * @public\n */\nexport const browserSessionPersistence: Persistence = BrowserSessionPersistence;\n", "/**\n * @license\n * Copyright 2019 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\n/** TODO: remove this once tslib has a polyfill for Promise.allSettled */\ninterface PromiseFulfilledResult<T> {\n  fulfilled: true;\n  value: T;\n}\n\ninterface PromiseRejectedResult {\n  fulfilled: false;\n  // eslint-disable-next-line @typescript-eslint/no-explicit-any\n  reason: any;\n}\n\nexport type PromiseSettledResult<T> =\n  | PromiseFulfilledResult<T>\n  | PromiseRejectedResult;\n\n/**\n * Shim for Promise.allSettled, note the slightly different format of `fulfilled` vs `status`.\n *\n * @param promises - Array of promises to wait on.\n */\nexport function _allSettled<T>(\n  promises: Array<Promise<T>>\n): Promise<Array<PromiseSettledResult<T>>> {\n  return Promise.all(\n    promises.map(async promise => {\n      try {\n        const value = await promise;\n        return {\n          fulfilled: true,\n          value\n        } as PromiseFulfilledResult<T>;\n      } catch (reason) {\n        return {\n          fulfilled: false,\n          reason\n        } as PromiseRejectedResult;\n      }\n    })\n  );\n}\n", "/**\n * @license\n * Copyright 2019 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport {\n  ReceiverHandler,\n  _EventType,\n  _ReceiverResponse,\n  SenderMessageEvent,\n  _Status,\n  _SenderRequest\n} from './index';\nimport { _allSettled } from './promise';\n\n/**\n * Interface class for receiving messages.\n *\n */\nexport class Receiver {\n  private static readonly receivers: Receiver[] = [];\n  private readonly boundEventHandler: EventListener;\n\n  private readonly handlersMap: {\n    // TypeScript doesn't have existential types :(\n    // eslint-disable-next-line @typescript-eslint/no-explicit-any\n    [eventType: string]: Set<ReceiverHandler<any, any>>;\n  } = {};\n\n  constructor(private readonly eventTarget: EventTarget) {\n    this.boundEventHandler = this.handleEvent.bind(this);\n  }\n\n  /**\n   * Obtain an instance of a Receiver for a given event target, if none exists it will be created.\n   *\n   * @param eventTarget - An event target (such as window or self) through which the underlying\n   * messages will be received.\n   */\n  static _getInstance(eventTarget: EventTarget): Receiver {\n    // The results are stored in an array since objects can't be keys for other\n    // objects. In addition, setting a unique property on an event target as a\n    // hash map key may not be allowed due to CORS restrictions.\n    const existingInstance = this.receivers.find(receiver =>\n      receiver.isListeningto(eventTarget)\n    );\n    if (existingInstance) {\n      return existingInstance;\n    }\n    const newInstance = new Receiver(eventTarget);\n    this.receivers.push(newInstance);\n    return newInstance;\n  }\n\n  private isListeningto(eventTarget: EventTarget): boolean {\n    return this.eventTarget === eventTarget;\n  }\n\n  /**\n   * Fans out a MessageEvent to the appropriate listeners.\n   *\n   * @remarks\n   * Sends an {@link Status.ACK} upon receipt and a {@link Status.DONE} once all handlers have\n   * finished processing.\n   *\n   * @param event - The MessageEvent.\n   *\n   */\n  private async handleEvent<\n    T extends _ReceiverResponse,\n    S extends _SenderRequest\n  >(event: Event): Promise<void> {\n    const messageEvent = event as MessageEvent<SenderMessageEvent<S>>;\n    const { eventId, eventType, data } = messageEvent.data;\n\n    const handlers: Set<ReceiverHandler<T, S>> | undefined =\n      this.handlersMap[eventType];\n    if (!handlers?.size) {\n      return;\n    }\n\n    messageEvent.ports[0].postMessage({\n      status: _Status.ACK,\n      eventId,\n      eventType\n    });\n\n    const promises = Array.from(handlers).map(async handler =>\n      handler(messageEvent.origin, data)\n    );\n    const response = await _allSettled(promises);\n    messageEvent.ports[0].postMessage({\n      status: _Status.DONE,\n      eventId,\n      eventType,\n      response\n    });\n  }\n\n  /**\n   * Subscribe an event handler for a particular event.\n   *\n   * @param eventType - Event name to subscribe to.\n   * @param eventHandler - The event handler which should receive the events.\n   *\n   */\n  _subscribe<T extends _ReceiverResponse, S extends _SenderRequest>(\n    eventType: _EventType,\n    eventHandler: ReceiverHandler<T, S>\n  ): void {\n    if (Object.keys(this.handlersMap).length === 0) {\n      this.eventTarget.addEventListener('message', this.boundEventHandler);\n    }\n\n    if (!this.handlersMap[eventType]) {\n      this.handlersMap[eventType] = new Set();\n    }\n\n    this.handlersMap[eventType].add(eventHandler);\n  }\n\n  /**\n   * Unsubscribe an event handler from a particular event.\n   *\n   * @param eventType - Event name to unsubscribe from.\n   * @param eventHandler - Optional event handler, if none provided, unsubscribe all handlers on this event.\n   *\n   */\n  _unsubscribe<T extends _ReceiverResponse, S extends _SenderRequest>(\n    eventType: _EventType,\n    eventHandler?: ReceiverHandler<T, S>\n  ): void {\n    if (this.handlersMap[eventType] && eventHandler) {\n      this.handlersMap[eventType].delete(eventHandler);\n    }\n    if (!eventHandler || this.handlersMap[eventType].size === 0) {\n      delete this.handlersMap[eventType];\n    }\n\n    if (Object.keys(this.handlersMap).length === 0) {\n      this.eventTarget.removeEventListener('message', this.boundEventHandler);\n    }\n  }\n}\n", "/**\n * @license\n * Copyright 2020 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nexport function _generateEventId(prefix = '', digits = 10): string {\n  let random = '';\n  for (let i = 0; i < digits; i++) {\n    random += Math.floor(Math.random() * 10);\n  }\n  return prefix + random;\n}\n", "/**\n * @license\n * Copyright 2019 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { _generateEventId } from '../../core/util/event_id';\nimport {\n  _SenderRequest,\n  _EventType,\n  ReceiverMessageEvent,\n  _MessageError,\n  SenderMessageEvent,\n  _Status,\n  _ReceiverMessageResponse,\n  _ReceiverResponse,\n  _TimeoutDuration\n} from './index';\n\ninterface MessageHandler {\n  messageChannel: MessageChannel;\n  onMessage: EventListenerOrEventListenerObject;\n}\n\n/**\n * Interface for sending messages and waiting for a completion response.\n *\n */\nexport class Sender {\n  private readonly handlers = new Set<MessageHandler>();\n\n  constructor(private readonly target: ServiceWorker) {}\n\n  /**\n   * Unsubscribe the handler and remove it from our tracking Set.\n   *\n   * @param handler - The handler to unsubscribe.\n   */\n  private removeMessageHandler(handler: MessageHandler): void {\n    if (handler.messageChannel) {\n      handler.messageChannel.port1.removeEventListener(\n        'message',\n        handler.onMessage\n      );\n      handler.messageChannel.port1.close();\n    }\n    this.handlers.delete(handler);\n  }\n\n  /**\n   * Send a message to the Receiver located at {@link target}.\n   *\n   * @remarks\n   * We'll first wait a bit for an ACK , if we get one we will wait significantly longer until the\n   * receiver has had a chance to fully process the event.\n   *\n   * @param eventType - Type of event to send.\n   * @param data - The payload of the event.\n   * @param timeout - Timeout for waiting on an ACK from the receiver.\n   *\n   * @returns An array of settled promises from all the handlers that were listening on the receiver.\n   */\n  async _send<T extends _ReceiverResponse, S extends _SenderRequest>(\n    eventType: _EventType,\n    data: S,\n    timeout = _TimeoutDuration.ACK\n  ): Promise<_ReceiverMessageResponse<T>> {\n    const messageChannel =\n      typeof MessageChannel !== 'undefined' ? new MessageChannel() : null;\n    if (!messageChannel) {\n      throw new Error(_MessageError.CONNECTION_UNAVAILABLE);\n    }\n    // Node timers and browser timers return fundamentally different types.\n    // We don't actually care what the value is but TS won't accept unknown and\n    // we can't cast properly in both environments.\n    // eslint-disable-next-line @typescript-eslint/no-explicit-any\n    let completionTimer: any;\n    let handler: MessageHandler;\n    return new Promise<_ReceiverMessageResponse<T>>((resolve, reject) => {\n      const eventId = _generateEventId('', 20);\n      messageChannel.port1.start();\n      const ackTimer = setTimeout(() => {\n        reject(new Error(_MessageError.UNSUPPORTED_EVENT));\n      }, timeout);\n      handler = {\n        messageChannel,\n        onMessage(event: Event): void {\n          const messageEvent = event as MessageEvent<ReceiverMessageEvent<T>>;\n          if (messageEvent.data.eventId !== eventId) {\n            return;\n          }\n          switch (messageEvent.data.status) {\n            case _Status.ACK:\n              // The receiver should ACK first.\n              clearTimeout(ackTimer);\n              completionTimer = setTimeout(() => {\n                reject(new Error(_MessageError.TIMEOUT));\n              }, _TimeoutDuration.COMPLETION);\n              break;\n            case _Status.DONE:\n              // Once the receiver's handlers are finished we will get the results.\n              clearTimeout(completionTimer);\n              resolve(messageEvent.data.response);\n              break;\n            default:\n              clearTimeout(ackTimer);\n              clearTimeout(completionTimer);\n              reject(new Error(_MessageError.INVALID_RESPONSE));\n              break;\n          }\n        }\n      };\n      this.handlers.add(handler);\n      messageChannel.port1.addEventListener('message', handler.onMessage);\n      this.target.postMessage(\n        {\n          eventType,\n          eventId,\n          data\n        } as SenderMessageEvent<S>,\n        [messageChannel.port2]\n      );\n    }).finally(() => {\n      if (handler) {\n        this.removeMessageHandler(handler);\n      }\n    });\n  }\n}\n", "/**\n * @license\n * Copyright 2019 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { Persistence } from '../../model/public_types';\nimport {\n  PersistedBlob,\n  PersistenceInternal as InternalPersistence,\n  PersistenceType,\n  PersistenceValue,\n  StorageEventListener,\n  STORAGE_AVAILABLE_KEY\n} from '../../core/persistence/';\nimport {\n  _EventType,\n  _PingResponse,\n  KeyChangedResponse,\n  KeyChangedRequest,\n  PingRequest,\n  _TimeoutDuration\n} from '../messagechannel/index';\nimport { Receiver } from '../messagechannel/receiver';\nimport { Sender } from '../messagechannel/sender';\nimport {\n  _isWorker,\n  _getActiveServiceWorker,\n  _getServiceWorkerController,\n  _getWorkerGlobalScope\n} from '../util/worker';\n\nexport const DB_NAME = 'firebaseLocalStorageDb';\nconst DB_VERSION = 1;\nconst DB_OBJECTSTORE_NAME = 'firebaseLocalStorage';\nconst DB_DATA_KEYPATH = 'fbase_key';\n\ninterface DBObject {\n  [DB_DATA_KEYPATH]: string;\n  value: PersistedBlob;\n}\n\n/**\n * Promise wrapper for IDBRequest\n *\n * Unfortunately we can't cleanly extend Promise<T> since promises are not callable in ES6\n *\n */\nclass DBPromise<T> {\n  constructor(private readonly request: IDBRequest) {}\n\n  toPromise(): Promise<T> {\n    return new Promise<T>((resolve, reject) => {\n      this.request.addEventListener('success', () => {\n        resolve(this.request.result);\n      });\n      this.request.addEventListener('error', () => {\n        reject(this.request.error);\n      });\n    });\n  }\n}\n\nfunction getObjectStore(db: IDBDatabase, isReadWrite: boolean): IDBObjectStore {\n  return db\n    .transaction([DB_OBJECTSTORE_NAME], isReadWrite ? 'readwrite' : 'readonly')\n    .objectStore(DB_OBJECTSTORE_NAME);\n}\n\nexport async function _clearDatabase(db: IDBDatabase): Promise<void> {\n  const objectStore = getObjectStore(db, true);\n  return new DBPromise<void>(objectStore.clear()).toPromise();\n}\n\nexport function _deleteDatabase(): Promise<void> {\n  const request = indexedDB.deleteDatabase(DB_NAME);\n  return new DBPromise<void>(request).toPromise();\n}\n\nexport function _openDatabase(): Promise<IDBDatabase> {\n  const request = indexedDB.open(DB_NAME, DB_VERSION);\n  return new Promise((resolve, reject) => {\n    request.addEventListener('error', () => {\n      reject(request.error);\n    });\n\n    request.addEventListener('upgradeneeded', () => {\n      const db = request.result;\n\n      try {\n        db.createObjectStore(DB_OBJECTSTORE_NAME, { keyPath: DB_DATA_KEYPATH });\n      } catch (e) {\n        reject(e);\n      }\n    });\n\n    request.addEventListener('success', async () => {\n      const db: IDBDatabase = request.result;\n      // Strange bug that occurs in Firefox when multiple tabs are opened at the\n      // same time. The only way to recover seems to be deleting the database\n      // and re-initializing it.\n      // https://github.com/firebase/firebase-js-sdk/issues/634\n\n      if (!db.objectStoreNames.contains(DB_OBJECTSTORE_NAME)) {\n        // Need to close the database or else you get a `blocked` event\n        db.close();\n        await _deleteDatabase();\n        resolve(await _openDatabase());\n      } else {\n        resolve(db);\n      }\n    });\n  });\n}\n\nexport async function _putObject(\n  db: IDBDatabase,\n  key: string,\n  value: PersistenceValue | string\n): Promise<void> {\n  const request = getObjectStore(db, true).put({\n    [DB_DATA_KEYPATH]: key,\n    value\n  });\n  return new DBPromise<void>(request).toPromise();\n}\n\nasync function getObject(\n  db: IDBDatabase,\n  key: string\n): Promise<PersistedBlob | null> {\n  const request = getObjectStore(db, false).get(key);\n  const data = await new DBPromise<DBObject | undefined>(request).toPromise();\n  return data === undefined ? null : data.value;\n}\n\nexport function _deleteObject(db: IDBDatabase, key: string): Promise<void> {\n  const request = getObjectStore(db, true).delete(key);\n  return new DBPromise<void>(request).toPromise();\n}\n\nexport const _POLLING_INTERVAL_MS = 800;\nexport const _TRANSACTION_RETRY_COUNT = 3;\n\nclass IndexedDBLocalPersistence implements InternalPersistence {\n  static type: 'LOCAL' = 'LOCAL';\n\n  type = PersistenceType.LOCAL;\n  db?: IDBDatabase;\n  readonly _shouldAllowMigration = true;\n\n  private readonly listeners: Record<string, Set<StorageEventListener>> = {};\n  private readonly localCache: Record<string, PersistenceValue | null> = {};\n  // setTimeout return value is platform specific\n  // eslint-disable-next-line @typescript-eslint/no-explicit-any\n  private pollTimer: any | null = null;\n  private pendingWrites = 0;\n\n  private receiver: Receiver | null = null;\n  private sender: Sender | null = null;\n  private serviceWorkerReceiverAvailable = false;\n  private activeServiceWorker: ServiceWorker | null = null;\n  // Visible for testing only\n  readonly _workerInitializationPromise: Promise<void>;\n\n  constructor() {\n    // Fire & forget the service worker registration as it may never resolve\n    this._workerInitializationPromise =\n      this.initializeServiceWorkerMessaging().then(\n        () => {},\n        () => {}\n      );\n  }\n\n  async _openDb(): Promise<IDBDatabase> {\n    if (this.db) {\n      return this.db;\n    }\n    this.db = await _openDatabase();\n    return this.db;\n  }\n\n  async _withRetries<T>(op: (db: IDBDatabase) => Promise<T>): Promise<T> {\n    let numAttempts = 0;\n\n    while (true) {\n      try {\n        const db = await this._openDb();\n        return await op(db);\n      } catch (e) {\n        if (numAttempts++ > _TRANSACTION_RETRY_COUNT) {\n          throw e;\n        }\n        if (this.db) {\n          this.db.close();\n          this.db = undefined;\n        }\n        // TODO: consider adding exponential backoff\n      }\n    }\n  }\n\n  /**\n   * IndexedDB events do not propagate from the main window to the worker context.  We rely on a\n   * postMessage interface to send these events to the worker ourselves.\n   */\n  private async initializeServiceWorkerMessaging(): Promise<void> {\n    return _isWorker() ? this.initializeReceiver() : this.initializeSender();\n  }\n\n  /**\n   * As the worker we should listen to events from the main window.\n   */\n  private async initializeReceiver(): Promise<void> {\n    this.receiver = Receiver._getInstance(_getWorkerGlobalScope()!);\n    // Refresh from persistence if we receive a KeyChanged message.\n    this.receiver._subscribe(\n      _EventType.KEY_CHANGED,\n      async (_origin: string, data: KeyChangedRequest) => {\n        const keys = await this._poll();\n        return {\n          keyProcessed: keys.includes(data.key)\n        };\n      }\n    );\n    // Let the sender know that we are listening so they give us more timeout.\n    this.receiver._subscribe(\n      _EventType.PING,\n      async (_origin: string, _data: PingRequest) => {\n        return [_EventType.KEY_CHANGED];\n      }\n    );\n  }\n\n  /**\n   * As the main window, we should let the worker know when keys change (set and remove).\n   *\n   * @remarks\n   * {@link https://developer.mozilla.org/en-US/docs/Web/API/ServiceWorkerContainer/ready | ServiceWorkerContainer.ready}\n   * may not resolve.\n   */\n  private async initializeSender(): Promise<void> {\n    // Check to see if there's an active service worker.\n    this.activeServiceWorker = await _getActiveServiceWorker();\n    if (!this.activeServiceWorker) {\n      return;\n    }\n    this.sender = new Sender(this.activeServiceWorker);\n    // Ping the service worker to check what events they can handle.\n    const results = await this.sender._send<_PingResponse, PingRequest>(\n      _EventType.PING,\n      {},\n      _TimeoutDuration.LONG_ACK\n    );\n    if (!results) {\n      return;\n    }\n    if (\n      results[0]?.fulfilled &&\n      results[0]?.value.includes(_EventType.KEY_CHANGED)\n    ) {\n      this.serviceWorkerReceiverAvailable = true;\n    }\n  }\n\n  /**\n   * Let the worker know about a changed key, the exact key doesn't technically matter since the\n   * worker will just trigger a full sync anyway.\n   *\n   * @remarks\n   * For now, we only support one service worker per page.\n   *\n   * @param key - Storage key which changed.\n   */\n  private async notifyServiceWorker(key: string): Promise<void> {\n    if (\n      !this.sender ||\n      !this.activeServiceWorker ||\n      _getServiceWorkerController() !== this.activeServiceWorker\n    ) {\n      return;\n    }\n    try {\n      await this.sender._send<KeyChangedResponse, KeyChangedRequest>(\n        _EventType.KEY_CHANGED,\n        { key },\n        // Use long timeout if receiver has previously responded to a ping from us.\n        this.serviceWorkerReceiverAvailable\n          ? _TimeoutDuration.LONG_ACK\n          : _TimeoutDuration.ACK\n      );\n    } catch {\n      // This is a best effort approach. Ignore errors.\n    }\n  }\n\n  async _isAvailable(): Promise<boolean> {\n    try {\n      if (!indexedDB) {\n        return false;\n      }\n      const db = await _openDatabase();\n      await _putObject(db, STORAGE_AVAILABLE_KEY, '1');\n      await _deleteObject(db, STORAGE_AVAILABLE_KEY);\n      return true;\n    } catch {}\n    return false;\n  }\n\n  private async _withPendingWrite(write: () => Promise<void>): Promise<void> {\n    this.pendingWrites++;\n    try {\n      await write();\n    } finally {\n      this.pendingWrites--;\n    }\n  }\n\n  async _set(key: string, value: PersistenceValue): Promise<void> {\n    return this._withPendingWrite(async () => {\n      await this._withRetries((db: IDBDatabase) => _putObject(db, key, value));\n      this.localCache[key] = value;\n      return this.notifyServiceWorker(key);\n    });\n  }\n\n  async _get<T extends PersistenceValue>(key: string): Promise<T | null> {\n    const obj = (await this._withRetries((db: IDBDatabase) =>\n      getObject(db, key)\n    )) as T;\n    this.localCache[key] = obj;\n    return obj;\n  }\n\n  async _remove(key: string): Promise<void> {\n    return this._withPendingWrite(async () => {\n      await this._withRetries((db: IDBDatabase) => _deleteObject(db, key));\n      delete this.localCache[key];\n      return this.notifyServiceWorker(key);\n    });\n  }\n\n  private async _poll(): Promise<string[]> {\n    // TODO: check if we need to fallback if getAll is not supported\n    const result = await this._withRetries((db: IDBDatabase) => {\n      const getAllRequest = getObjectStore(db, false).getAll();\n      return new DBPromise<DBObject[] | null>(getAllRequest).toPromise();\n    });\n\n    if (!result) {\n      return [];\n    }\n\n    // If we have pending writes in progress abort, we'll get picked up on the next poll\n    if (this.pendingWrites !== 0) {\n      return [];\n    }\n\n    const keys = [];\n    const keysInResult = new Set();\n    if (result.length !== 0) {\n      for (const { fbase_key: key, value } of result) {\n        keysInResult.add(key);\n        if (JSON.stringify(this.localCache[key]) !== JSON.stringify(value)) {\n          this.notifyListeners(key, value as PersistenceValue);\n          keys.push(key);\n        }\n      }\n    }\n\n    for (const localKey of Object.keys(this.localCache)) {\n      if (this.localCache[localKey] && !keysInResult.has(localKey)) {\n        // Deleted\n        this.notifyListeners(localKey, null);\n        keys.push(localKey);\n      }\n    }\n    return keys;\n  }\n\n  private notifyListeners(\n    key: string,\n    newValue: PersistenceValue | null\n  ): void {\n    this.localCache[key] = newValue;\n    const listeners = this.listeners[key];\n    if (listeners) {\n      for (const listener of Array.from(listeners)) {\n        listener(newValue);\n      }\n    }\n  }\n\n  private startPolling(): void {\n    this.stopPolling();\n\n    this.pollTimer = setInterval(\n      async () => this._poll(),\n      _POLLING_INTERVAL_MS\n    );\n  }\n\n  private stopPolling(): void {\n    if (this.pollTimer) {\n      clearInterval(this.pollTimer);\n      this.pollTimer = null;\n    }\n  }\n\n  _addListener(key: string, listener: StorageEventListener): void {\n    if (Object.keys(this.listeners).length === 0) {\n      this.startPolling();\n    }\n    if (!this.listeners[key]) {\n      this.listeners[key] = new Set();\n      // Populate the cache to avoid spuriously triggering on first poll.\n      void this._get(key); // This can happen in the background async and we can return immediately.\n    }\n    this.listeners[key].add(listener);\n  }\n\n  _removeListener(key: string, listener: StorageEventListener): void {\n    if (this.listeners[key]) {\n      this.listeners[key].delete(listener);\n\n      if (this.listeners[key].size === 0) {\n        delete this.listeners[key];\n      }\n    }\n\n    if (Object.keys(this.listeners).length === 0) {\n      this.stopPolling();\n    }\n  }\n}\n\n/**\n * An implementation of {@link Persistence} of type `LOCAL` using `indexedDB`\n * for the underlying storage.\n *\n * @public\n */\nexport const indexedDBLocalPersistence: Persistence = IndexedDBLocalPersistence;\n", "/**\n * @license\n * Copyright 2021 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { PopupRedirectResolver } from '../../model/public_types';\nimport { AuthInternal } from '../../model/auth';\nimport { PopupRedirectResolverInternal } from '../../model/popup_redirect';\nimport { AuthErrorCode } from '../errors';\nimport { _assert } from './assert';\nimport { _getInstance } from './instantiator';\n\n/**\n * Chooses a popup/redirect resolver to use. This prefers the override (which\n * is directly passed in), and falls back to the property set on the auth\n * object. If neither are available, this function errors w/ an argument error.\n */\nexport function _withDefaultResolver(\n  auth: AuthInternal,\n  resolverOverride: PopupRedirectResolver | undefined\n): PopupRedirectResolverInternal {\n  if (resolverOverride) {\n    return _getInstance(resolverOverride);\n  }\n\n  _assert(auth._popupRedirectResolver, auth, AuthErrorCode.ARGUMENT_ERROR);\n\n  return auth._popupRedirectResolver;\n}\n", "/**\n * @license\n * Copyright 2019 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport {\n  signInWithIdp,\n  SignInWithIdpRequest\n} from '../../api/authentication/idp';\nimport { PhoneOrOauthTokenResponse } from '../../api/authentication/mfa';\nimport { AuthInternal } from '../../model/auth';\nimport { IdTokenResponse } from '../../model/id_token';\nimport { UserInternal, UserCredentialInternal } from '../../model/user';\nimport { AuthCredential } from '../credentials';\nimport { _link as _linkUser } from '../user/link_unlink';\nimport { _reauthenticate } from '../user/reauthenticate';\nimport { _assert } from '../util/assert';\nimport { _signInWithCredential } from './credential';\nimport { AuthErrorCode } from '../errors';\nimport { ProviderId } from '../../model/enums';\n\nexport interface IdpTaskParams {\n  auth: AuthInternal;\n  requestUri: string;\n  sessionId?: string;\n  tenantId?: string;\n  postBody?: string;\n  pendingToken?: string;\n  user?: UserInternal;\n  bypassAuthState?: boolean;\n}\n\nexport type IdpTask = (\n  params: IdpTaskParams\n) => Promise<UserCredentialInternal>;\n\nclass IdpCredential extends AuthCredential {\n  constructor(readonly params: IdpTaskParams) {\n    super(ProviderId.CUSTOM, ProviderId.CUSTOM);\n  }\n\n  _getIdTokenResponse(auth: AuthInternal): Promise<PhoneOrOauthTokenResponse> {\n    return signInWithIdp(auth, this._buildIdpRequest());\n  }\n\n  _linkToIdToken(\n    auth: AuthInternal,\n    idToken: string\n  ): Promise<IdTokenResponse> {\n    return signInWithIdp(auth, this._buildIdpRequest(idToken));\n  }\n\n  _getReauthenticationResolver(auth: AuthInternal): Promise<IdTokenResponse> {\n    return signInWithIdp(auth, this._buildIdpRequest());\n  }\n\n  private _buildIdpRequest(idToken?: string): SignInWithIdpRequest {\n    const request: SignInWithIdpRequest = {\n      requestUri: this.params.requestUri,\n      sessionId: this.params.sessionId,\n      postBody: this.params.postBody,\n      tenantId: this.params.tenantId,\n      pendingToken: this.params.pendingToken,\n      returnSecureToken: true,\n      returnIdpCredential: true\n    };\n\n    if (idToken) {\n      request.idToken = idToken;\n    }\n\n    return request;\n  }\n}\n\nexport function _signIn(\n  params: IdpTaskParams\n): Promise<UserCredentialInternal> {\n  return _signInWithCredential(\n    params.auth,\n    new IdpCredential(params),\n    params.bypassAuthState\n  ) as Promise<UserCredentialInternal>;\n}\n\nexport function _reauth(\n  params: IdpTaskParams\n): Promise<UserCredentialInternal> {\n  const { auth, user } = params;\n  _assert(user, auth, AuthErrorCode.INTERNAL_ERROR);\n  return _reauthenticate(\n    user,\n    new IdpCredential(params),\n    params.bypassAuthState\n  );\n}\n\nexport async function _link(\n  params: IdpTaskParams\n): Promise<UserCredentialInternal> {\n  const { auth, user } = params;\n  _assert(user, auth, AuthErrorCode.INTERNAL_ERROR);\n  return _linkUser(user, new IdpCredential(params), params.bypassAuthState);\n}\n", "/**\n * @license\n * Copyright 2020 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { FirebaseError } from '@firebase/util';\n\nimport {\n  AuthEvent,\n  AuthEventConsumer,\n  AuthEventType,\n  EventManager,\n  PopupRedirectResolverInternal\n} from '../../model/popup_redirect';\nimport { UserInternal, UserCredentialInternal } from '../../model/user';\nimport { AuthErrorCode } from '../errors';\nimport { debugAssert, _fail } from '../util/assert';\nimport {\n  _link,\n  _reauth,\n  _signIn,\n  IdpTask,\n  IdpTaskParams\n} from '../strategies/idp';\nimport { AuthInternal } from '../../model/auth';\n\ninterface PendingPromise {\n  resolve: (cred: UserCredentialInternal | null) => void;\n  reject: (error: Error) => void;\n}\n\n/**\n * Popup event manager. Handles the popup's entire lifecycle; listens to auth\n * events\n */\nexport abstract class AbstractPopupRedirectOperation\n  implements AuthEventConsumer\n{\n  private pendingPromise: PendingPromise | null = null;\n  private eventManager: EventManager | null = null;\n  readonly filter: AuthEventType[];\n\n  abstract eventId: string | null;\n\n  constructor(\n    protected readonly auth: AuthInternal,\n    filter: AuthEventType | AuthEventType[],\n    protected readonly resolver: PopupRedirectResolverInternal,\n    protected user?: UserInternal,\n    protected readonly bypassAuthState = false\n  ) {\n    this.filter = Array.isArray(filter) ? filter : [filter];\n  }\n\n  abstract onExecution(): Promise<void>;\n\n  execute(): Promise<UserCredentialInternal | null> {\n    return new Promise<UserCredentialInternal | null>(\n      async (resolve, reject) => {\n        this.pendingPromise = { resolve, reject };\n\n        try {\n          this.eventManager = await this.resolver._initialize(this.auth);\n          await this.onExecution();\n          this.eventManager.registerConsumer(this);\n        } catch (e) {\n          this.reject(e as Error);\n        }\n      }\n    );\n  }\n\n  async onAuthEvent(event: AuthEvent): Promise<void> {\n    const { urlResponse, sessionId, postBody, tenantId, error, type } = event;\n    if (error) {\n      this.reject(error);\n      return;\n    }\n\n    const params: IdpTaskParams = {\n      auth: this.auth,\n      requestUri: urlResponse!,\n      sessionId: sessionId!,\n      tenantId: tenantId || undefined,\n      postBody: postBody || undefined,\n      user: this.user,\n      bypassAuthState: this.bypassAuthState\n    };\n\n    try {\n      this.resolve(await this.getIdpTask(type)(params));\n    } catch (e) {\n      this.reject(e as Error);\n    }\n  }\n\n  onError(error: FirebaseError): void {\n    this.reject(error);\n  }\n\n  private getIdpTask(type: AuthEventType): IdpTask {\n    switch (type) {\n      case AuthEventType.SIGN_IN_VIA_POPUP:\n      case AuthEventType.SIGN_IN_VIA_REDIRECT:\n        return _signIn;\n      case AuthEventType.LINK_VIA_POPUP:\n      case AuthEventType.LINK_VIA_REDIRECT:\n        return _link;\n      case AuthEventType.REAUTH_VIA_POPUP:\n      case AuthEventType.REAUTH_VIA_REDIRECT:\n        return _reauth;\n      default:\n        _fail(this.auth, AuthErrorCode.INTERNAL_ERROR);\n    }\n  }\n\n  protected resolve(cred: UserCredentialInternal | null): void {\n    debugAssert(this.pendingPromise, 'Pending promise was never set');\n    this.pendingPromise.resolve(cred);\n    this.unregisterAndCleanUp();\n  }\n\n  protected reject(error: Error): void {\n    debugAssert(this.pendingPromise, 'Pending promise was never set');\n    this.pendingPromise.reject(error);\n    this.unregisterAndCleanUp();\n  }\n\n  private unregisterAndCleanUp(): void {\n    if (this.eventManager) {\n      this.eventManager.unregisterConsumer(this);\n    }\n\n    this.pendingPromise = null;\n    this.cleanUp();\n  }\n\n  abstract cleanUp(): void;\n}\n", "/**\n * @license\n * Copyright 2020 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport {\n  Auth,\n  AuthProvider,\n  PopupRedirectResolver,\n  User,\n  UserCredential\n} from '../../model/public_types';\n\nimport { _castAuth } from '../../core/auth/auth_impl';\nimport { AuthErrorCode } from '../../core/errors';\nimport {\n  _assert,\n  debugAssert,\n  _createError,\n  _assertInstanceOf\n} from '../../core/util/assert';\nimport { Delay } from '../../core/util/delay';\nimport { _generateEventId } from '../../core/util/event_id';\nimport { AuthInternal } from '../../model/auth';\nimport {\n  AuthEventType,\n  PopupRedirectResolverInternal\n} from '../../model/popup_redirect';\nimport { UserInternal } from '../../model/user';\nimport { _withDefaultResolver } from '../../core/util/resolver';\nimport { AuthPopup } from '../util/popup';\nimport { AbstractPopupRedirectOperation } from '../../core/strategies/abstract_popup_redirect_operation';\nimport { FederatedAuthProvider } from '../../core/providers/federated';\nimport { getModularInstance } from '@firebase/util';\nimport { _isFirebaseServerApp } from '@firebase/app';\n\n/*\n * The event timeout is the same on mobile and desktop, no need for Delay. Set this to 8s since\n * blocking functions can take upto 7s to complete sign in, as documented in:\n * https://cloud.google.com/identity-platform/docs/blocking-functions#understanding_blocking_functions\n * https://firebase.google.com/docs/auth/extend-with-blocking-functions#understanding_blocking_functions\n */\nexport const enum _Timeout {\n  AUTH_EVENT = 8000\n}\nexport const _POLL_WINDOW_CLOSE_TIMEOUT = new Delay(2000, 10000);\n\n/**\n * Authenticates a Firebase client using a popup-based OAuth authentication flow.\n *\n * @remarks\n * If succeeds, returns the signed in user along with the provider's credential. If sign in was\n * unsuccessful, returns an error object containing additional information about the error.\n *\n * This method does not work in a Node.js environment or with {@link Auth} instances created with a\n * {@link @firebase/app#FirebaseServerApp}.\n *\n * @example\n * ```javascript\n * // Sign in using a popup.\n * const provider = new FacebookAuthProvider();\n * const result = await signInWithPopup(auth, provider);\n *\n * // The signed-in user info.\n * const user = result.user;\n * // This gives you a Facebook Access Token.\n * const credential = provider.credentialFromResult(auth, result);\n * const token = credential.accessToken;\n * ```\n *\n * @param auth - The {@link Auth} instance.\n * @param provider - The provider to authenticate. The provider has to be an {@link OAuthProvider}.\n * Non-OAuth providers like {@link EmailAuthProvider} will throw an error.\n * @param resolver - An instance of {@link PopupRedirectResolver}, optional\n * if already supplied to {@link initializeAuth} or provided by {@link getAuth}.\n *\n * @public\n */\nexport async function signInWithPopup(\n  auth: Auth,\n  provider: AuthProvider,\n  resolver?: PopupRedirectResolver\n): Promise<UserCredential> {\n  if (_isFirebaseServerApp(auth.app)) {\n    return Promise.reject(\n      _createError(auth, AuthErrorCode.OPERATION_NOT_SUPPORTED)\n    );\n  }\n  const authInternal = _castAuth(auth);\n  _assertInstanceOf(auth, provider, FederatedAuthProvider);\n  const resolverInternal = _withDefaultResolver(authInternal, resolver);\n  const action = new PopupOperation(\n    authInternal,\n    AuthEventType.SIGN_IN_VIA_POPUP,\n    provider,\n    resolverInternal\n  );\n  return action.executeNotNull();\n}\n\n/**\n * Reauthenticates the current user with the specified {@link OAuthProvider} using a pop-up based\n * OAuth flow.\n *\n * @remarks\n * If the reauthentication is successful, the returned result will contain the user and the\n * provider's credential.\n *\n * This method does not work in a Node.js environment or on any {@link User} signed in by\n * {@link Auth} instances created with a {@link @firebase/app#FirebaseServerApp}.\n *\n * @example\n * ```javascript\n * // Sign in using a popup.\n * const provider = new FacebookAuthProvider();\n * const result = await signInWithPopup(auth, provider);\n * // Reauthenticate using a popup.\n * await reauthenticateWithPopup(result.user, provider);\n * ```\n *\n * @param user - The user.\n * @param provider - The provider to authenticate. The provider has to be an {@link OAuthProvider}.\n * Non-OAuth providers like {@link EmailAuthProvider} will throw an error.\n * @param resolver - An instance of {@link PopupRedirectResolver}, optional\n * if already supplied to {@link initializeAuth} or provided by {@link getAuth}.\n *\n * @public\n */\nexport async function reauthenticateWithPopup(\n  user: User,\n  provider: AuthProvider,\n  resolver?: PopupRedirectResolver\n): Promise<UserCredential> {\n  const userInternal = getModularInstance(user) as UserInternal;\n  if (_isFirebaseServerApp(userInternal.auth.app)) {\n    return Promise.reject(\n      _createError(userInternal.auth, AuthErrorCode.OPERATION_NOT_SUPPORTED)\n    );\n  }\n  _assertInstanceOf(userInternal.auth, provider, FederatedAuthProvider);\n  const resolverInternal = _withDefaultResolver(userInternal.auth, resolver);\n  const action = new PopupOperation(\n    userInternal.auth,\n    AuthEventType.REAUTH_VIA_POPUP,\n    provider,\n    resolverInternal,\n    userInternal\n  );\n  return action.executeNotNull();\n}\n\n/**\n * Links the authenticated provider to the user account using a pop-up based OAuth flow.\n *\n * @remarks\n * If the linking is successful, the returned result will contain the user and the provider's credential.\n *\n * This method does not work in a Node.js environment.\n *\n * @example\n * ```javascript\n * // Sign in using some other provider.\n * const result = await signInWithEmailAndPassword(auth, email, password);\n * // Link using a popup.\n * const provider = new FacebookAuthProvider();\n * await linkWithPopup(result.user, provider);\n * ```\n *\n * @param user - The user.\n * @param provider - The provider to authenticate. The provider has to be an {@link OAuthProvider}.\n * Non-OAuth providers like {@link EmailAuthProvider} will throw an error.\n * @param resolver - An instance of {@link PopupRedirectResolver}, optional\n * if already supplied to {@link initializeAuth} or provided by {@link getAuth}.\n *\n * @public\n */\nexport async function linkWithPopup(\n  user: User,\n  provider: AuthProvider,\n  resolver?: PopupRedirectResolver\n): Promise<UserCredential> {\n  const userInternal = getModularInstance(user) as UserInternal;\n  _assertInstanceOf(userInternal.auth, provider, FederatedAuthProvider);\n  const resolverInternal = _withDefaultResolver(userInternal.auth, resolver);\n\n  const action = new PopupOperation(\n    userInternal.auth,\n    AuthEventType.LINK_VIA_POPUP,\n    provider,\n    resolverInternal,\n    userInternal\n  );\n  return action.executeNotNull();\n}\n\n/**\n * Popup event manager. Handles the popup's entire lifecycle; listens to auth\n * events\n *\n */\nclass PopupOperation extends AbstractPopupRedirectOperation {\n  // Only one popup is ever shown at once. The lifecycle of the current popup\n  // can be managed / cancelled by the constructor.\n  private static currentPopupAction: PopupOperation | null = null;\n  private authWindow: AuthPopup | null = null;\n  private pollId: number | null = null;\n\n  constructor(\n    auth: AuthInternal,\n    filter: AuthEventType,\n    private readonly provider: AuthProvider,\n    resolver: PopupRedirectResolverInternal,\n    user?: UserInternal\n  ) {\n    super(auth, filter, resolver, user);\n    if (PopupOperation.currentPopupAction) {\n      PopupOperation.currentPopupAction.cancel();\n    }\n\n    PopupOperation.currentPopupAction = this;\n  }\n\n  async executeNotNull(): Promise<UserCredential> {\n    const result = await this.execute();\n    _assert(result, this.auth, AuthErrorCode.INTERNAL_ERROR);\n    return result;\n  }\n\n  async onExecution(): Promise<void> {\n    debugAssert(\n      this.filter.length === 1,\n      'Popup operations only handle one event'\n    );\n    const eventId = _generateEventId();\n    this.authWindow = await this.resolver._openPopup(\n      this.auth,\n      this.provider,\n      this.filter[0], // There's always one, see constructor\n      eventId\n    );\n    this.authWindow.associatedEvent = eventId;\n\n    // Check for web storage support and origin validation _after_ the popup is\n    // loaded. These operations are slow (~1 second or so) Rather than\n    // waiting on them before opening the window, optimistically open the popup\n    // and check for storage support at the same time. If storage support is\n    // not available, this will cause the whole thing to reject properly. It\n    // will also close the popup, but since the promise has already rejected,\n    // the popup closed by user poll will reject into the void.\n    this.resolver._originValidation(this.auth).catch(e => {\n      this.reject(e);\n    });\n\n    this.resolver._isIframeWebStorageSupported(this.auth, isSupported => {\n      if (!isSupported) {\n        this.reject(\n          _createError(this.auth, AuthErrorCode.WEB_STORAGE_UNSUPPORTED)\n        );\n      }\n    });\n\n    // Handle user closure. Notice this does *not* use await\n    this.pollUserCancellation();\n  }\n\n  get eventId(): string | null {\n    return this.authWindow?.associatedEvent || null;\n  }\n\n  cancel(): void {\n    this.reject(_createError(this.auth, AuthErrorCode.EXPIRED_POPUP_REQUEST));\n  }\n\n  cleanUp(): void {\n    if (this.authWindow) {\n      this.authWindow.close();\n    }\n\n    if (this.pollId) {\n      window.clearTimeout(this.pollId);\n    }\n\n    this.authWindow = null;\n    this.pollId = null;\n    PopupOperation.currentPopupAction = null;\n  }\n\n  private pollUserCancellation(): void {\n    const poll = (): void => {\n      if (this.authWindow?.window?.closed) {\n        // Make sure that there is sufficient time for whatever action to\n        // complete. The window could have closed but the sign in network\n        // call could still be in flight. This is specifically true for\n        // Firefox or if the opener is in an iframe, in which case the oauth\n        // helper closes the popup.\n        this.pollId = window.setTimeout(() => {\n          this.pollId = null;\n          this.reject(\n            _createError(this.auth, AuthErrorCode.POPUP_CLOSED_BY_USER)\n          );\n        }, _Timeout.AUTH_EVENT);\n        return;\n      }\n\n      this.pollId = window.setTimeout(poll, _POLL_WINDOW_CLOSE_TIMEOUT.get());\n    };\n\n    poll();\n  }\n}\n", "/**\n * @license\n * Copyright 2020 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { AuthInternal } from '../../model/auth';\nimport {\n  AuthEvent,\n  AuthEventType,\n  PopupRedirectResolverInternal\n} from '../../model/popup_redirect';\nimport { UserCredentialInternal } from '../../model/user';\nimport { PersistenceInternal } from '../persistence';\nimport { _persistenceKeyName } from '../persistence/persistence_user_manager';\nimport { _getInstance } from '../util/instantiator';\nimport { AbstractPopupRedirectOperation } from './abstract_popup_redirect_operation';\n\nconst PENDING_REDIRECT_KEY = 'pendingRedirect';\n\n// We only get one redirect outcome for any one auth, so just store it\n// in here.\nconst redirectOutcomeMap: Map<\n  string,\n  () => Promise<UserCredentialInternal | null>\n> = new Map();\n\nexport class RedirectAction extends AbstractPopupRedirectOperation {\n  eventId = null;\n\n  constructor(\n    auth: AuthInternal,\n    resolver: PopupRedirectResolverInternal,\n    bypassAuthState = false\n  ) {\n    super(\n      auth,\n      [\n        AuthEventType.SIGN_IN_VIA_REDIRECT,\n        AuthEventType.LINK_VIA_REDIRECT,\n        AuthEventType.REAUTH_VIA_REDIRECT,\n        AuthEventType.UNKNOWN\n      ],\n      resolver,\n      undefined,\n      bypassAuthState\n    );\n  }\n\n  /**\n   * Override the execute function; if we already have a redirect result, then\n   * just return it.\n   */\n  async execute(): Promise<UserCredentialInternal | null> {\n    let readyOutcome = redirectOutcomeMap.get(this.auth._key());\n    if (!readyOutcome) {\n      try {\n        const hasPendingRedirect = await _getAndClearPendingRedirectStatus(\n          this.resolver,\n          this.auth\n        );\n        const result = hasPendingRedirect ? await super.execute() : null;\n        readyOutcome = () => Promise.resolve(result);\n      } catch (e) {\n        readyOutcome = () => Promise.reject(e);\n      }\n\n      redirectOutcomeMap.set(this.auth._key(), readyOutcome);\n    }\n\n    // If we're not bypassing auth state, the ready outcome should be set to\n    // null.\n    if (!this.bypassAuthState) {\n      redirectOutcomeMap.set(this.auth._key(), () => Promise.resolve(null));\n    }\n\n    return readyOutcome();\n  }\n\n  async onAuthEvent(event: AuthEvent): Promise<void> {\n    if (event.type === AuthEventType.SIGN_IN_VIA_REDIRECT) {\n      return super.onAuthEvent(event);\n    } else if (event.type === AuthEventType.UNKNOWN) {\n      // This is a sentinel value indicating there's no pending redirect\n      this.resolve(null);\n      return;\n    }\n\n    if (event.eventId) {\n      const user = await this.auth._redirectUserForId(event.eventId);\n      if (user) {\n        this.user = user;\n        return super.onAuthEvent(event);\n      } else {\n        this.resolve(null);\n      }\n    }\n  }\n\n  async onExecution(): Promise<void> {}\n\n  cleanUp(): void {}\n}\n\nexport async function _getAndClearPendingRedirectStatus(\n  resolver: PopupRedirectResolverInternal,\n  auth: AuthInternal\n): Promise<boolean> {\n  const key = pendingRedirectKey(auth);\n  const persistence = resolverPersistence(resolver);\n  if (!(await persistence._isAvailable())) {\n    return false;\n  }\n  const hasPendingRedirect = (await persistence._get(key)) === 'true';\n  await persistence._remove(key);\n  return hasPendingRedirect;\n}\n\nexport async function _setPendingRedirectStatus(\n  resolver: PopupRedirectResolverInternal,\n  auth: AuthInternal\n): Promise<void> {\n  return resolverPersistence(resolver)._set(pendingRedirectKey(auth), 'true');\n}\n\nexport function _clearRedirectOutcomes(): void {\n  redirectOutcomeMap.clear();\n}\n\nexport function _overrideRedirectResult(\n  auth: AuthInternal,\n  result: () => Promise<UserCredentialInternal | null>\n): void {\n  redirectOutcomeMap.set(auth._key(), result);\n}\n\nfunction resolverPersistence(\n  resolver: PopupRedirectResolverInternal\n): PersistenceInternal {\n  return _getInstance(resolver._redirectPersistence);\n}\n\nfunction pendingRedirectKey(auth: AuthInternal): string {\n  return _persistenceKeyName(\n    PENDING_REDIRECT_KEY,\n    auth.config.apiKey,\n    auth.name\n  );\n}\n", "/**\n * @license\n * Copyright 2020 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport {\n  Auth,\n  AuthProvider,\n  PopupRedirectResolver,\n  User,\n  UserCredential\n} from '../../model/public_types';\n\nimport { _castAuth } from '../../core/auth/auth_impl';\nimport { _assertLinkedStatus } from '../../core/user/link_unlink';\nimport {\n  _assertInstanceOf,\n  _serverAppCurrentUserOperationNotSupportedError\n} from '../../core/util/assert';\nimport { _generateEventId } from '../../core/util/event_id';\nimport { AuthEventType } from '../../model/popup_redirect';\nimport { UserInternal } from '../../model/user';\nimport { _withDefaultResolver } from '../../core/util/resolver';\nimport {\n  RedirectAction,\n  _setPendingRedirectStatus\n} from '../../core/strategies/redirect';\nimport { FederatedAuthProvider } from '../../core/providers/federated';\nimport { getModularInstance } from '@firebase/util';\nimport { _isFirebaseServerApp } from '@firebase/app';\n\n/**\n * Authenticates a Firebase client using a full-page redirect flow.\n *\n * @remarks\n * To handle the results and errors for this operation, refer to {@link getRedirectResult}.\n * Follow the {@link https://firebase.google.com/docs/auth/web/redirect-best-practices\n * | best practices} when using {@link signInWithRedirect}.\n *\n * This method does not work in a Node.js environment or with {@link Auth} instances created with a\n * {@link @firebase/app#FirebaseServerApp}.\n *\n * @example\n * ```javascript\n * // Sign in using a redirect.\n * const provider = new FacebookAuthProvider();\n * // You can add additional scopes to the provider:\n * provider.addScope('user_birthday');\n * // Start a sign in process for an unauthenticated user.\n * await signInWithRedirect(auth, provider);\n * // This will trigger a full page redirect away from your app\n *\n * // After returning from the redirect when your app initializes you can obtain the result\n * const result = await getRedirectResult(auth);\n * if (result) {\n *   // This is the signed-in user\n *   const user = result.user;\n *   // This gives you a Facebook Access Token.\n *   const credential = provider.credentialFromResult(auth, result);\n *   const token = credential.accessToken;\n * }\n * // As this API can be used for sign-in, linking and reauthentication,\n * // check the operationType to determine what triggered this redirect\n * // operation.\n * const operationType = result.operationType;\n * ```\n *\n * @param auth - The {@link Auth} instance.\n * @param provider - The provider to authenticate. The provider has to be an {@link OAuthProvider}.\n * Non-OAuth providers like {@link EmailAuthProvider} will throw an error.\n * @param resolver - An instance of {@link PopupRedirectResolver}, optional\n * if already supplied to {@link initializeAuth} or provided by {@link getAuth}.\n *\n * @public\n */\nexport function signInWithRedirect(\n  auth: Auth,\n  provider: AuthProvider,\n  resolver?: PopupRedirectResolver\n): Promise<never> {\n  return _signInWithRedirect(auth, provider, resolver) as Promise<never>;\n}\n\nexport async function _signInWithRedirect(\n  auth: Auth,\n  provider: AuthProvider,\n  resolver?: PopupRedirectResolver\n): Promise<void | never> {\n  if (_isFirebaseServerApp(auth.app)) {\n    return Promise.reject(\n      _serverAppCurrentUserOperationNotSupportedError(auth)\n    );\n  }\n  const authInternal = _castAuth(auth);\n  _assertInstanceOf(auth, provider, FederatedAuthProvider);\n  // Wait for auth initialization to complete, this will process pending redirects and clear the\n  // PENDING_REDIRECT_KEY in persistence. This should be completed before starting a new\n  // redirect and creating a PENDING_REDIRECT_KEY entry.\n  await authInternal._initializationPromise;\n  const resolverInternal = _withDefaultResolver(authInternal, resolver);\n  await _setPendingRedirectStatus(resolverInternal, authInternal);\n\n  return resolverInternal._openRedirect(\n    authInternal,\n    provider,\n    AuthEventType.SIGN_IN_VIA_REDIRECT\n  );\n}\n\n/**\n * Reauthenticates the current user with the specified {@link OAuthProvider} using a full-page redirect flow.\n * @remarks\n * To handle the results and errors for this operation, refer to {@link getRedirectResult}.\n * Follow the {@link https://firebase.google.com/docs/auth/web/redirect-best-practices\n * | best practices} when using {@link reauthenticateWithRedirect}.\n *\n * This method does not work in a Node.js environment or with {@link Auth} instances\n * created with a {@link @firebase/app#FirebaseServerApp}.\n *\n * @example\n * ```javascript\n * // Sign in using a redirect.\n * const provider = new FacebookAuthProvider();\n * const result = await signInWithRedirect(auth, provider);\n * // This will trigger a full page redirect away from your app\n *\n * // After returning from the redirect when your app initializes you can obtain the result\n * const result = await getRedirectResult(auth);\n * // Reauthenticate using a redirect.\n * await reauthenticateWithRedirect(result.user, provider);\n * // This will again trigger a full page redirect away from your app\n *\n * // After returning from the redirect when your app initializes you can obtain the result\n * const result = await getRedirectResult(auth);\n * ```\n *\n * @param user - The user.\n * @param provider - The provider to authenticate. The provider has to be an {@link OAuthProvider}.\n * Non-OAuth providers like {@link EmailAuthProvider} will throw an error.\n * @param resolver - An instance of {@link PopupRedirectResolver}, optional\n * if already supplied to {@link initializeAuth} or provided by {@link getAuth}.\n *\n * @public\n */\nexport function reauthenticateWithRedirect(\n  user: User,\n  provider: AuthProvider,\n  resolver?: PopupRedirectResolver\n): Promise<never> {\n  return _reauthenticateWithRedirect(\n    user,\n    provider,\n    resolver\n  ) as Promise<never>;\n}\nexport async function _reauthenticateWithRedirect(\n  user: User,\n  provider: AuthProvider,\n  resolver?: PopupRedirectResolver\n): Promise<void | never> {\n  const userInternal = getModularInstance(user) as UserInternal;\n  _assertInstanceOf(userInternal.auth, provider, FederatedAuthProvider);\n  if (_isFirebaseServerApp(userInternal.auth.app)) {\n    return Promise.reject(\n      _serverAppCurrentUserOperationNotSupportedError(userInternal.auth)\n    );\n  }\n  // Wait for auth initialization to complete, this will process pending redirects and clear the\n  // PENDING_REDIRECT_KEY in persistence. This should be completed before starting a new\n  // redirect and creating a PENDING_REDIRECT_KEY entry.\n  await userInternal.auth._initializationPromise;\n  // Allow the resolver to error before persisting the redirect user\n  const resolverInternal = _withDefaultResolver(userInternal.auth, resolver);\n  await _setPendingRedirectStatus(resolverInternal, userInternal.auth);\n\n  const eventId = await prepareUserForRedirect(userInternal);\n  return resolverInternal._openRedirect(\n    userInternal.auth,\n    provider,\n    AuthEventType.REAUTH_VIA_REDIRECT,\n    eventId\n  );\n}\n\n/**\n * Links the {@link OAuthProvider} to the user account using a full-page redirect flow.\n * @remarks\n * To handle the results and errors for this operation, refer to {@link getRedirectResult}.\n * Follow the {@link https://firebase.google.com/docs/auth/web/redirect-best-practices\n * | best practices} when using {@link linkWithRedirect}.\n *\n * This method does not work in a Node.js environment or with {@link Auth} instances\n * created with a {@link @firebase/app#FirebaseServerApp}.\n *\n * @example\n * ```javascript\n * // Sign in using some other provider.\n * const result = await signInWithEmailAndPassword(auth, email, password);\n * // Link using a redirect.\n * const provider = new FacebookAuthProvider();\n * await linkWithRedirect(result.user, provider);\n * // This will trigger a full page redirect away from your app\n *\n * // After returning from the redirect when your app initializes you can obtain the result\n * const result = await getRedirectResult(auth);\n * ```\n *\n * @param user - The user.\n * @param provider - The provider to authenticate. The provider has to be an {@link OAuthProvider}.\n * Non-OAuth providers like {@link EmailAuthProvider} will throw an error.\n * @param resolver - An instance of {@link PopupRedirectResolver}, optional\n * if already supplied to {@link initializeAuth} or provided by {@link getAuth}.\n *\n * @public\n */\nexport function linkWithRedirect(\n  user: User,\n  provider: AuthProvider,\n  resolver?: PopupRedirectResolver\n): Promise<never> {\n  return _linkWithRedirect(user, provider, resolver) as Promise<never>;\n}\nexport async function _linkWithRedirect(\n  user: User,\n  provider: AuthProvider,\n  resolver?: PopupRedirectResolver\n): Promise<void | never> {\n  const userInternal = getModularInstance(user) as UserInternal;\n  _assertInstanceOf(userInternal.auth, provider, FederatedAuthProvider);\n  // Wait for auth initialization to complete, this will process pending redirects and clear the\n  // PENDING_REDIRECT_KEY in persistence. This should be completed before starting a new\n  // redirect and creating a PENDING_REDIRECT_KEY entry.\n  await userInternal.auth._initializationPromise;\n  // Allow the resolver to error before persisting the redirect user\n  const resolverInternal = _withDefaultResolver(userInternal.auth, resolver);\n  await _assertLinkedStatus(false, userInternal, provider.providerId);\n  await _setPendingRedirectStatus(resolverInternal, userInternal.auth);\n\n  const eventId = await prepareUserForRedirect(userInternal);\n  return resolverInternal._openRedirect(\n    userInternal.auth,\n    provider,\n    AuthEventType.LINK_VIA_REDIRECT,\n    eventId\n  );\n}\n\n/**\n * Returns a {@link UserCredential} from the redirect-based sign-in flow.\n *\n * @remarks\n * If sign-in succeeded, returns the signed in user. If sign-in was unsuccessful, fails with an\n * error. If no redirect operation was called, returns `null`.\n *\n * This method does not work in a Node.js environment or with {@link Auth} instances created with a\n * {@link @firebase/app#FirebaseServerApp}.\n *\n * @example\n * ```javascript\n * // Sign in using a redirect.\n * const provider = new FacebookAuthProvider();\n * // You can add additional scopes to the provider:\n * provider.addScope('user_birthday');\n * // Start a sign in process for an unauthenticated user.\n * await signInWithRedirect(auth, provider);\n * // This will trigger a full page redirect away from your app\n *\n * // After returning from the redirect when your app initializes you can obtain the result\n * const result = await getRedirectResult(auth);\n * if (result) {\n *   // This is the signed-in user\n *   const user = result.user;\n *   // This gives you a Facebook Access Token.\n *   const credential = provider.credentialFromResult(auth, result);\n *   const token = credential.accessToken;\n * }\n * // As this API can be used for sign-in, linking and reauthentication,\n * // check the operationType to determine what triggered this redirect\n * // operation.\n * const operationType = result.operationType;\n * ```\n *\n * @param auth - The {@link Auth} instance.\n * @param resolver - An instance of {@link PopupRedirectResolver}, optional\n * if already supplied to {@link initializeAuth} or provided by {@link getAuth}.\n *\n * @public\n */\nexport async function getRedirectResult(\n  auth: Auth,\n  resolver?: PopupRedirectResolver\n): Promise<UserCredential | null> {\n  await _castAuth(auth)._initializationPromise;\n  return _getRedirectResult(auth, resolver, false);\n}\n\nexport async function _getRedirectResult(\n  auth: Auth,\n  resolverExtern?: PopupRedirectResolver,\n  bypassAuthState = false\n): Promise<UserCredential | null> {\n  if (_isFirebaseServerApp(auth.app)) {\n    return Promise.reject(\n      _serverAppCurrentUserOperationNotSupportedError(auth)\n    );\n  }\n  const authInternal = _castAuth(auth);\n  const resolver = _withDefaultResolver(authInternal, resolverExtern);\n  const action = new RedirectAction(authInternal, resolver, bypassAuthState);\n  const result = await action.execute();\n\n  if (result && !bypassAuthState) {\n    delete result.user._redirectEventId;\n    await authInternal._persistUserIfCurrent(result.user as UserInternal);\n    await authInternal._setRedirectUser(null, resolverExtern);\n  }\n\n  return result;\n}\n\nasync function prepareUserForRedirect(user: UserInternal): Promise<string> {\n  const eventId = _generateEventId(`${user.uid}:::`);\n  user._redirectEventId = eventId;\n  await user.auth._setRedirectUser(user);\n  await user.auth._persistUserIfCurrent(user);\n  return eventId;\n}\n", "/**\n * @license\n * Copyright 2020 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport {\n  AuthEvent,\n  AuthEventConsumer,\n  AuthEventType,\n  EventManager\n} from '../../model/popup_redirect';\nimport { AuthErrorCode } from '../errors';\nimport { AuthInternal } from '../../model/auth';\nimport { _createError } from '../util/assert';\n\n// The amount of time to store the UIDs of seen events; this is\n// set to 10 min by default\nconst EVENT_DUPLICATION_CACHE_DURATION_MS = 10 * 60 * 1000;\n\nexport class AuthEventManager implements EventManager {\n  private readonly cachedEventUids: Set<string> = new Set();\n  private readonly consumers: Set<AuthEventConsumer> = new Set();\n  protected queuedRedirectEvent: AuthEvent | null = null;\n  protected hasHandledPotentialRedirect = false;\n  private lastProcessedEventTime = Date.now();\n\n  constructor(private readonly auth: AuthInternal) {}\n\n  registerConsumer(authEventConsumer: AuthEventConsumer): void {\n    this.consumers.add(authEventConsumer);\n\n    if (\n      this.queuedRedirectEvent &&\n      this.isEventForConsumer(this.queuedRedirectEvent, authEventConsumer)\n    ) {\n      this.sendToConsumer(this.queuedRedirectEvent, authEventConsumer);\n      this.saveEventToCache(this.queuedRedirectEvent);\n      this.queuedRedirectEvent = null;\n    }\n  }\n\n  unregisterConsumer(authEventConsumer: AuthEventConsumer): void {\n    this.consumers.delete(authEventConsumer);\n  }\n\n  onEvent(event: AuthEvent): boolean {\n    // Check if the event has already been handled\n    if (this.hasEventBeenHandled(event)) {\n      return false;\n    }\n\n    let handled = false;\n    this.consumers.forEach(consumer => {\n      if (this.isEventForConsumer(event, consumer)) {\n        handled = true;\n        this.sendToConsumer(event, consumer);\n        this.saveEventToCache(event);\n      }\n    });\n\n    if (this.hasHandledPotentialRedirect || !isRedirectEvent(event)) {\n      // If we've already seen a redirect before, or this is a popup event,\n      // bail now\n      return handled;\n    }\n\n    this.hasHandledPotentialRedirect = true;\n\n    // If the redirect wasn't handled, hang on to it\n    if (!handled) {\n      this.queuedRedirectEvent = event;\n      handled = true;\n    }\n\n    return handled;\n  }\n\n  private sendToConsumer(event: AuthEvent, consumer: AuthEventConsumer): void {\n    if (event.error && !isNullRedirectEvent(event)) {\n      const code =\n        (event.error.code?.split('auth/')[1] as AuthErrorCode) ||\n        AuthErrorCode.INTERNAL_ERROR;\n      consumer.onError(_createError(this.auth, code));\n    } else {\n      consumer.onAuthEvent(event);\n    }\n  }\n\n  private isEventForConsumer(\n    event: AuthEvent,\n    consumer: AuthEventConsumer\n  ): boolean {\n    const eventIdMatches =\n      consumer.eventId === null ||\n      (!!event.eventId && event.eventId === consumer.eventId);\n    return consumer.filter.includes(event.type) && eventIdMatches;\n  }\n\n  private hasEventBeenHandled(event: AuthEvent): boolean {\n    if (\n      Date.now() - this.lastProcessedEventTime >=\n      EVENT_DUPLICATION_CACHE_DURATION_MS\n    ) {\n      this.cachedEventUids.clear();\n    }\n\n    return this.cachedEventUids.has(eventUid(event));\n  }\n\n  private saveEventToCache(event: AuthEvent): void {\n    this.cachedEventUids.add(eventUid(event));\n    this.lastProcessedEventTime = Date.now();\n  }\n}\n\nfunction eventUid(e: AuthEvent): string {\n  return [e.type, e.eventId, e.sessionId, e.tenantId].filter(v => v).join('-');\n}\n\nfunction isNullRedirectEvent({ type, error }: AuthEvent): boolean {\n  return (\n    type === AuthEventType.UNKNOWN &&\n    error?.code === `auth/${AuthErrorCode.NO_AUTH_EVENT}`\n  );\n}\n\nfunction isRedirectEvent(event: AuthEvent): boolean {\n  switch (event.type) {\n    case AuthEventType.SIGN_IN_VIA_REDIRECT:\n    case AuthEventType.LINK_VIA_REDIRECT:\n    case AuthEventType.REAUTH_VIA_REDIRECT:\n      return true;\n    case AuthEventType.UNKNOWN:\n      return isNullRedirectEvent(event);\n    default:\n      return false;\n  }\n}\n", "/**\n * @license\n * Copyright 2020 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { _performApiRequest, Endpoint, HttpMethod } from '../index';\nimport { Auth } from '../../model/public_types';\n\nexport interface GetProjectConfigRequest {\n  androidPackageName?: string;\n  iosBundleId?: string;\n}\n\nexport interface GetProjectConfigResponse {\n  authorizedDomains: string[];\n}\n\nexport async function _getProjectConfig(\n  auth: Auth,\n  request: GetProjectConfigRequest = {}\n): Promise<GetProjectConfigResponse> {\n  return _performApiRequest<GetProjectConfigRequest, GetProjectConfigResponse>(\n    auth,\n    HttpMethod.GET,\n    Endpoint.GET_PROJECT_CONFIG,\n    request\n  );\n}\n", "/**\n * @license\n * Copyright 2020 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { _getProjectConfig } from '../../api/project_config/get_project_config';\nimport { AuthInternal } from '../../model/auth';\nimport { AuthErrorCode } from '../errors';\nimport { _fail } from './assert';\nimport { _getCurrentUrl } from './location';\n\nconst IP_ADDRESS_REGEX = /^\\d{1,3}\\.\\d{1,3}\\.\\d{1,3}\\.\\d{1,3}$/;\nconst HTTP_REGEX = /^https?/;\n\nexport async function _validateOrigin(auth: AuthInternal): Promise<void> {\n  // Skip origin validation if we are in an emulated environment\n  if (auth.config.emulator) {\n    return;\n  }\n\n  const { authorizedDomains } = await _getProjectConfig(auth);\n\n  for (const domain of authorizedDomains) {\n    try {\n      if (matchDomain(domain)) {\n        return;\n      }\n    } catch {\n      // Do nothing if there's a URL error; just continue searching\n    }\n  }\n\n  // In the old SDK, this error also provides helpful messages.\n  _fail(auth, AuthErrorCode.INVALID_ORIGIN);\n}\n\nfunction matchDomain(expected: string): boolean {\n  const currentUrl = _getCurrentUrl();\n  const { protocol, hostname } = new URL(currentUrl);\n  if (expected.startsWith('chrome-extension://')) {\n    const ceUrl = new URL(expected);\n\n    if (ceUrl.hostname === '' && hostname === '') {\n      // For some reason we're not parsing chrome URLs properly\n      return (\n        protocol === 'chrome-extension:' &&\n        expected.replace('chrome-extension://', '') ===\n          currentUrl.replace('chrome-extension://', '')\n      );\n    }\n\n    return protocol === 'chrome-extension:' && ceUrl.hostname === hostname;\n  }\n\n  if (!HTTP_REGEX.test(protocol)) {\n    return false;\n  }\n\n  if (IP_ADDRESS_REGEX.test(expected)) {\n    // The domain has to be exactly equal to the pattern, as an IP domain will\n    // only contain the IP, no extra character.\n    return hostname === expected;\n  }\n\n  // Dots in pattern should be escaped.\n  const escapedDomainPattern = expected.replace(/\\./g, '\\\\.');\n  // Non ip address domains.\n  // domain.com = *.domain.com OR domain.com\n  const re = new RegExp(\n    '^(.+\\\\.' + escapedDomainPattern + '|' + escapedDomainPattern + ')$',\n    'i'\n  );\n  return re.test(hostname);\n}\n", "/**\n * @license\n * Copyright 2020 Google LLC.\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { AuthErrorCode } from '../../core/errors';\nimport { _createError } from '../../core/util/assert';\nimport { Delay } from '../../core/util/delay';\nimport { AuthInternal } from '../../model/auth';\nimport { _window } from '../auth_window';\nimport * as js from '../load_js';\n\nconst NETWORK_TIMEOUT = new Delay(30000, 60000);\n\n/**\n * Reset unloaded GApi modules. If gapi.load fails due to a network error,\n * it will stop working after a retrial. This is a hack to fix this issue.\n */\nfunction resetUnloadedGapiModules(): void {\n  // Clear last failed gapi.load state to force next gapi.load to first\n  // load the failed gapi.iframes module.\n  // Get gapix.beacon context.\n  const beacon = _window().___jsl;\n  // Get current hint.\n  if (beacon?.H) {\n    // Get gapi hint.\n    for (const hint of Object.keys(beacon.H)) {\n      // Requested modules.\n      beacon.H[hint].r = beacon.H[hint].r || [];\n      // Loaded modules.\n      beacon.H[hint].L = beacon.H[hint].L || [];\n      // Set requested modules to a copy of the loaded modules.\n      beacon.H[hint].r = [...beacon.H[hint].L];\n      // Clear pending callbacks.\n      if (beacon.CP) {\n        for (let i = 0; i < beacon.CP.length; i++) {\n          // Remove all failed pending callbacks.\n          beacon.CP[i] = null;\n        }\n      }\n    }\n  }\n}\n\nfunction loadGapi(auth: AuthInternal): Promise<gapi.iframes.Context> {\n  return new Promise<gapi.iframes.Context>((resolve, reject) => {\n    // Function to run when gapi.load is ready.\n    function loadGapiIframe(): void {\n      // The developer may have tried to previously run gapi.load and failed.\n      // Run this to fix that.\n      resetUnloadedGapiModules();\n      gapi.load('gapi.iframes', {\n        callback: () => {\n          resolve(gapi.iframes.getContext());\n        },\n        ontimeout: () => {\n          // The above reset may be sufficient, but having this reset after\n          // failure ensures that if the developer calls gapi.load after the\n          // connection is re-established and before another attempt to embed\n          // the iframe, it would work and would not be broken because of our\n          // failed attempt.\n          // Timeout when gapi.iframes.Iframe not loaded.\n          resetUnloadedGapiModules();\n          reject(_createError(auth, AuthErrorCode.NETWORK_REQUEST_FAILED));\n        },\n        timeout: NETWORK_TIMEOUT.get()\n      });\n    }\n\n    if (_window().gapi?.iframes?.Iframe) {\n      // If gapi.iframes.Iframe available, resolve.\n      resolve(gapi.iframes.getContext());\n    } else if (!!_window().gapi?.load) {\n      // Gapi loader ready, load gapi.iframes.\n      loadGapiIframe();\n    } else {\n      // Create a new iframe callback when this is called so as not to overwrite\n      // any previous defined callback. This happens if this method is called\n      // multiple times in parallel and could result in the later callback\n      // overwriting the previous one. This would end up with a iframe\n      // timeout.\n      const cbName = js._generateCallbackName('iframefcb');\n      // GApi loader not available, dynamically load platform.js.\n      _window()[cbName] = () => {\n        // GApi loader should be ready.\n        if (!!gapi.load) {\n          loadGapiIframe();\n        } else {\n          // Gapi loader failed, throw error.\n          reject(_createError(auth, AuthErrorCode.NETWORK_REQUEST_FAILED));\n        }\n      };\n      // Load GApi loader.\n      return js\n        ._loadJS(`${js._gapiScriptUrl()}?onload=${cbName}`)\n        .catch(e => reject(e));\n    }\n  }).catch(error => {\n    // Reset cached promise to allow for retrial.\n    cachedGApiLoader = null;\n    throw error;\n  });\n}\n\nlet cachedGApiLoader: Promise<gapi.iframes.Context> | null = null;\nexport function _loadGapi(auth: AuthInternal): Promise<gapi.iframes.Context> {\n  cachedGApiLoader = cachedGApiLoader || loadGapi(auth);\n  return cachedGApiLoader;\n}\n\nexport function _resetLoader(): void {\n  cachedGApiLoader = null;\n}\n", "/**\n * @license\n * Copyright 2020 Google LLC.\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { SDK_VERSION } from '@firebase/app';\nimport { querystring } from '@firebase/util';\nimport { DefaultConfig } from '../../../internal';\n\nimport { AuthErrorCode } from '../../core/errors';\nimport { _assert, _createError } from '../../core/util/assert';\nimport { Delay } from '../../core/util/delay';\nimport { _emulatorUrl } from '../../core/util/emulator';\nimport { AuthInternal } from '../../model/auth';\nimport { _window } from '../auth_window';\nimport * as gapiLoader from './gapi';\n\nconst PING_TIMEOUT = new Delay(5000, 15000);\nconst IFRAME_PATH = '__/auth/iframe';\nconst EMULATED_IFRAME_PATH = 'emulator/auth/iframe';\n\nconst IFRAME_ATTRIBUTES = {\n  style: {\n    position: 'absolute',\n    top: '-100px',\n    width: '1px',\n    height: '1px'\n  },\n  'aria-hidden': 'true',\n  tabindex: '-1'\n};\n\n// Map from apiHost to endpoint ID for passing into iframe. In current SDK, apiHost can be set to\n// anything (not from a list of endpoints with IDs as in legacy), so this is the closest we can get.\nconst EID_FROM_APIHOST = new Map([\n  [DefaultConfig.API_HOST, 'p'], // production\n  ['staging-identitytoolkit.sandbox.googleapis.com', 's'], // staging\n  ['test-identitytoolkit.sandbox.googleapis.com', 't'] // test\n]);\n\nfunction getIframeUrl(auth: AuthInternal): string {\n  const config = auth.config;\n  _assert(config.authDomain, auth, AuthErrorCode.MISSING_AUTH_DOMAIN);\n  const url = config.emulator\n    ? _emulatorUrl(config, EMULATED_IFRAME_PATH)\n    : `https://${auth.config.authDomain}/${IFRAME_PATH}`;\n\n  const params: Record<string, string> = {\n    apiKey: config.apiKey,\n    appName: auth.name,\n    v: SDK_VERSION\n  };\n  const eid = EID_FROM_APIHOST.get(auth.config.apiHost);\n  if (eid) {\n    params.eid = eid;\n  }\n  const frameworks = auth._getFrameworks();\n  if (frameworks.length) {\n    params.fw = frameworks.join(',');\n  }\n  return `${url}?${querystring(params).slice(1)}`;\n}\n\nexport async function _openIframe(\n  auth: AuthInternal\n): Promise<gapi.iframes.Iframe> {\n  const context = await gapiLoader._loadGapi(auth);\n  const gapi = _window().gapi;\n  _assert(gapi, auth, AuthErrorCode.INTERNAL_ERROR);\n  return context.open(\n    {\n      where: document.body,\n      url: getIframeUrl(auth),\n      messageHandlersFilter: gapi.iframes.CROSS_ORIGIN_IFRAMES_FILTER,\n      attributes: IFRAME_ATTRIBUTES,\n      dontclear: true\n    },\n    (iframe: gapi.iframes.Iframe) =>\n      new Promise(async (resolve, reject) => {\n        await iframe.restyle({\n          // Prevent iframe from closing on mouse out.\n          setHideOnLeave: false\n        });\n\n        const networkError = _createError(\n          auth,\n          AuthErrorCode.NETWORK_REQUEST_FAILED\n        );\n        // Confirm iframe is correctly loaded.\n        // To fallback on failure, set a timeout.\n        const networkErrorTimer = _window().setTimeout(() => {\n          reject(networkError);\n        }, PING_TIMEOUT.get());\n        // Clear timer and resolve pending iframe ready promise.\n        function clearTimerAndResolve(): void {\n          _window().clearTimeout(networkErrorTimer);\n          resolve(iframe);\n        }\n        // This returns an IThenable. However the reject part does not call\n        // when the iframe is not loaded.\n        iframe.ping(clearTimerAndResolve).then(clearTimerAndResolve, () => {\n          reject(networkError);\n        });\n      })\n  );\n}\n", "/**\n * @license\n * Copyright 2020 Google LLC.\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { getUA } from '@firebase/util';\n\nimport { AuthErrorCode } from '../../core/errors';\nimport { _assert } from '../../core/util/assert';\nimport {\n  _isChromeIOS,\n  _isFirefox,\n  _isIOSStandalone\n} from '../../core/util/browser';\nimport { AuthInternal } from '../../model/auth';\n\nconst BASE_POPUP_OPTIONS = {\n  location: 'yes',\n  resizable: 'yes',\n  statusbar: 'yes',\n  toolbar: 'no'\n};\n\nconst DEFAULT_WIDTH = 500;\nconst DEFAULT_HEIGHT = 600;\nconst TARGET_BLANK = '_blank';\n\nconst FIREFOX_EMPTY_URL = 'http://localhost';\n\nexport class AuthPopup {\n  associatedEvent: string | null = null;\n\n  constructor(readonly window: Window | null) {}\n\n  close(): void {\n    if (this.window) {\n      try {\n        this.window.close();\n      } catch (e) {}\n    }\n  }\n}\n\nexport function _open(\n  auth: AuthInternal,\n  url?: string,\n  name?: string,\n  width = DEFAULT_WIDTH,\n  height = DEFAULT_HEIGHT\n): AuthPopup {\n  const top = Math.max((window.screen.availHeight - height) / 2, 0).toString();\n  const left = Math.max((window.screen.availWidth - width) / 2, 0).toString();\n  let target = '';\n\n  const options: { [key: string]: string } = {\n    ...BASE_POPUP_OPTIONS,\n    width: width.toString(),\n    height: height.toString(),\n    top,\n    left\n  };\n\n  // Chrome iOS 7 and 8 is returning an undefined popup win when target is\n  // specified, even though the popup is not necessarily blocked.\n  const ua = getUA().toLowerCase();\n\n  if (name) {\n    target = _isChromeIOS(ua) ? TARGET_BLANK : name;\n  }\n\n  if (_isFirefox(ua)) {\n    // Firefox complains when invalid URLs are popped out. Hacky way to bypass.\n    url = url || FIREFOX_EMPTY_URL;\n    // Firefox disables by default scrolling on popup windows, which can create\n    // issues when the user has many Google accounts, for instance.\n    options.scrollbars = 'yes';\n  }\n\n  const optionsString = Object.entries(options).reduce(\n    (accum, [key, value]) => `${accum}${key}=${value},`,\n    ''\n  );\n\n  if (_isIOSStandalone(ua) && target !== '_self') {\n    openAsNewWindowIOS(url || '', target);\n    return new AuthPopup(null);\n  }\n\n  // about:blank getting sanitized causing browsers like IE/Edge to display\n  // brief error message before redirecting to handler.\n  const newWin = window.open(url || '', target, optionsString);\n  _assert(newWin, auth, AuthErrorCode.POPUP_BLOCKED);\n\n  // Flaky on IE edge, encapsulate with a try and catch.\n  try {\n    newWin.focus();\n  } catch (e) {}\n\n  return new AuthPopup(newWin);\n}\n\nfunction openAsNewWindowIOS(url: string, target: string): void {\n  const el = document.createElement('a');\n  el.href = url;\n  el.target = target;\n  const click = document.createEvent('MouseEvent');\n  click.initMouseEvent(\n    'click',\n    true,\n    true,\n    window,\n    1,\n    0,\n    0,\n    0,\n    0,\n    false,\n    false,\n    false,\n    false,\n    1,\n    null\n  );\n  el.dispatchEvent(click);\n}\n", "/**\n * @license\n * Copyright 2021 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { SDK_VERSION } from '@firebase/app';\nimport { AuthProvider } from '../../model/public_types';\nimport { ApiKey, AppName, AuthInternal } from '../../model/auth';\nimport { AuthEventType } from '../../model/popup_redirect';\nimport { AuthErrorCode } from '../errors';\nimport { _assert } from './assert';\nimport { isEmpty, querystring } from '@firebase/util';\nimport { _emulatorUrl } from './emulator';\nimport { FederatedAuthProvider } from '../providers/federated';\nimport { BaseOAuthProvider } from '../providers/oauth';\n\n/**\n * URL for Authentication widget which will initiate the OAuth handshake\n *\n * @internal\n */\nconst WIDGET_PATH = '__/auth/handler';\n\n/**\n * URL for emulated environment\n *\n * @internal\n */\nconst EMULATOR_WIDGET_PATH = 'emulator/auth/handler';\n\n/**\n * Fragment name for the App Check token that gets passed to the widget\n *\n * @internal\n */\nconst FIREBASE_APP_CHECK_FRAGMENT_ID = encodeURIComponent('fac');\n\n// eslint-disable-next-line @typescript-eslint/consistent-type-definitions\ntype WidgetParams = {\n  apiKey: ApiKey;\n  appName: AppName;\n  authType: AuthEventType;\n  redirectUrl?: string;\n  v: string;\n  providerId?: string;\n  scopes?: string;\n  customParameters?: string;\n  eventId?: string;\n  tid?: string;\n} & { [key: string]: string | undefined };\n\nexport async function _getRedirectUrl(\n  auth: AuthInternal,\n  provider: AuthProvider,\n  authType: AuthEventType,\n  redirectUrl?: string,\n  eventId?: string,\n  additionalParams?: Record<string, string>\n): Promise<string> {\n  _assert(auth.config.authDomain, auth, AuthErrorCode.MISSING_AUTH_DOMAIN);\n  _assert(auth.config.apiKey, auth, AuthErrorCode.INVALID_API_KEY);\n\n  const params: WidgetParams = {\n    apiKey: auth.config.apiKey,\n    appName: auth.name,\n    authType,\n    redirectUrl,\n    v: SDK_VERSION,\n    eventId\n  };\n\n  if (provider instanceof FederatedAuthProvider) {\n    provider.setDefaultLanguage(auth.languageCode);\n    params.providerId = provider.providerId || '';\n    if (!isEmpty(provider.getCustomParameters())) {\n      params.customParameters = JSON.stringify(provider.getCustomParameters());\n    }\n\n    // TODO set additionalParams from the provider as well?\n    for (const [key, value] of Object.entries(additionalParams || {})) {\n      params[key] = value;\n    }\n  }\n\n  if (provider instanceof BaseOAuthProvider) {\n    const scopes = provider.getScopes().filter(scope => scope !== '');\n    if (scopes.length > 0) {\n      params.scopes = scopes.join(',');\n    }\n  }\n\n  if (auth.tenantId) {\n    params.tid = auth.tenantId;\n  }\n\n  // TODO: maybe set eid as endpointId\n  // TODO: maybe set fw as Frameworks.join(\",\")\n\n  const paramsDict = params as Record<string, string | number>;\n  for (const key of Object.keys(paramsDict)) {\n    if (paramsDict[key] === undefined) {\n      delete paramsDict[key];\n    }\n  }\n\n  // Sets the App Check token to pass to the widget\n  const appCheckToken = await auth._getAppCheckToken();\n  const appCheckTokenFragment = appCheckToken\n    ? `#${FIREBASE_APP_CHECK_FRAGMENT_ID}=${encodeURIComponent(appCheckToken)}`\n    : '';\n\n  // Start at index 1 to skip the leading '&' in the query string\n  return `${getHandlerBase(auth)}?${querystring(paramsDict).slice(\n    1\n  )}${appCheckTokenFragment}`;\n}\n\nfunction getHandlerBase({ config }: AuthInternal): string {\n  if (!config.emulator) {\n    return `https://${config.authDomain}/${WIDGET_PATH}`;\n  }\n\n  return _emulatorUrl(config, EMULATOR_WIDGET_PATH);\n}\n", "/**\n * @license\n * Copyright 2020 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { AuthProvider, PopupRedirectResolver } from '../model/public_types';\n\nimport { AuthEventManager } from '../core/auth/auth_event_manager';\nimport { AuthErrorCode } from '../core/errors';\nimport { _assert, debugAssert, _fail } from '../core/util/assert';\nimport { _generateEventId } from '../core/util/event_id';\nimport { _getCurrentUrl } from '../core/util/location';\nimport { _validateOrigin } from '../core/util/validate_origin';\nimport { AuthInternal } from '../model/auth';\nimport {\n  AuthEventType,\n  EventManager,\n  GapiAuthEvent,\n  GapiOutcome,\n  PopupRedirectResolverInternal\n} from '../model/popup_redirect';\nimport { _setWindowLocation } from './auth_window';\nimport { _openIframe } from './iframe/iframe';\nimport { browserSessionPersistence } from './persistence/session_storage';\nimport { _open, AuthPopup } from './util/popup';\nimport { _getRedirectResult } from './strategies/redirect';\nimport { _getRedirectUrl } from '../core/util/handler';\nimport { _isIOS, _isMobileBrowser, _isSafari } from '../core/util/browser';\nimport { _overrideRedirectResult } from '../core/strategies/redirect';\n\n/**\n * The special web storage event\n *\n */\nconst WEB_STORAGE_SUPPORT_KEY = 'webStorageSupport';\n\ninterface WebStorageSupportMessage extends gapi.iframes.Message {\n  [index: number]: Record<string, boolean>;\n}\n\ninterface ManagerOrPromise {\n  manager?: EventManager;\n  promise?: Promise<EventManager>;\n}\n\nclass BrowserPopupRedirectResolver implements PopupRedirectResolverInternal {\n  private readonly eventManagers: Record<string, ManagerOrPromise> = {};\n  private readonly iframes: Record<string, gapi.iframes.Iframe> = {};\n  private readonly originValidationPromises: Record<string, Promise<void>> = {};\n\n  readonly _redirectPersistence = browserSessionPersistence;\n\n  // Wrapping in async even though we don't await anywhere in order\n  // to make sure errors are raised as promise rejections\n  async _openPopup(\n    auth: AuthInternal,\n    provider: AuthProvider,\n    authType: AuthEventType,\n    eventId?: string\n  ): Promise<AuthPopup> {\n    debugAssert(\n      this.eventManagers[auth._key()]?.manager,\n      '_initialize() not called before _openPopup()'\n    );\n\n    const url = await _getRedirectUrl(\n      auth,\n      provider,\n      authType,\n      _getCurrentUrl(),\n      eventId\n    );\n    return _open(auth, url, _generateEventId());\n  }\n\n  async _openRedirect(\n    auth: AuthInternal,\n    provider: AuthProvider,\n    authType: AuthEventType,\n    eventId?: string\n  ): Promise<never> {\n    await this._originValidation(auth);\n    const url = await _getRedirectUrl(\n      auth,\n      provider,\n      authType,\n      _getCurrentUrl(),\n      eventId\n    );\n    _setWindowLocation(url);\n    return new Promise(() => {});\n  }\n\n  _initialize(auth: AuthInternal): Promise<EventManager> {\n    const key = auth._key();\n    if (this.eventManagers[key]) {\n      const { manager, promise } = this.eventManagers[key];\n      if (manager) {\n        return Promise.resolve(manager);\n      } else {\n        debugAssert(promise, 'If manager is not set, promise should be');\n        return promise;\n      }\n    }\n\n    const promise = this.initAndGetManager(auth);\n    this.eventManagers[key] = { promise };\n\n    // If the promise is rejected, the key should be removed so that the\n    // operation can be retried later.\n    promise.catch(() => {\n      delete this.eventManagers[key];\n    });\n\n    return promise;\n  }\n\n  private async initAndGetManager(auth: AuthInternal): Promise<EventManager> {\n    const iframe = await _openIframe(auth);\n    const manager = new AuthEventManager(auth);\n    iframe.register<GapiAuthEvent>(\n      'authEvent',\n      (iframeEvent: GapiAuthEvent | null) => {\n        _assert(iframeEvent?.authEvent, auth, AuthErrorCode.INVALID_AUTH_EVENT);\n        // TODO: Consider splitting redirect and popup events earlier on\n\n        const handled = manager.onEvent(iframeEvent.authEvent);\n        return { status: handled ? GapiOutcome.ACK : GapiOutcome.ERROR };\n      },\n      gapi.iframes.CROSS_ORIGIN_IFRAMES_FILTER\n    );\n\n    this.eventManagers[auth._key()] = { manager };\n    this.iframes[auth._key()] = iframe;\n    return manager;\n  }\n\n  _isIframeWebStorageSupported(\n    auth: AuthInternal,\n    cb: (supported: boolean) => unknown\n  ): void {\n    const iframe = this.iframes[auth._key()];\n    iframe.send<gapi.iframes.Message, WebStorageSupportMessage>(\n      WEB_STORAGE_SUPPORT_KEY,\n      { type: WEB_STORAGE_SUPPORT_KEY },\n      result => {\n        const isSupported = result?.[0]?.[WEB_STORAGE_SUPPORT_KEY];\n        if (isSupported !== undefined) {\n          cb(!!isSupported);\n        }\n\n        _fail(auth, AuthErrorCode.INTERNAL_ERROR);\n      },\n      gapi.iframes.CROSS_ORIGIN_IFRAMES_FILTER\n    );\n  }\n\n  _originValidation(auth: AuthInternal): Promise<void> {\n    const key = auth._key();\n    if (!this.originValidationPromises[key]) {\n      this.originValidationPromises[key] = _validateOrigin(auth);\n    }\n\n    return this.originValidationPromises[key];\n  }\n\n  get _shouldInitProactively(): boolean {\n    // Mobile browsers and Safari need to optimistically initialize\n    return _isMobileBrowser() || _isSafari() || _isIOS();\n  }\n\n  _completeRedirectFn = _getRedirectResult;\n\n  _overrideRedirectResult = _overrideRedirectResult;\n}\n\n/**\n * An implementation of {@link PopupRedirectResolver} suitable for browser\n * based applications.\n *\n * @remarks\n * This method does not work in a Node.js environment.\n *\n * @public\n */\nexport const browserPopupRedirectResolver: PopupRedirectResolver =\n  BrowserPopupRedirectResolver;\n", "/**\n * @license\n * Copyright 2021 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { FirebaseApp, getApp, _getProvider } from '@firebase/app';\n\nimport {\n  initializeAuth,\n  beforeAuthStateChanged,\n  onIdTokenChanged,\n  connectAuthEmulator\n} from '..';\nimport { registerAuth } from '../core/auth/register';\nimport { ClientPlatform } from '../core/util/version';\nimport { browserLocalPersistence } from './persistence/local_storage';\nimport { browserSessionPersistence } from './persistence/session_storage';\nimport { indexedDBLocalPersistence } from './persistence/indexed_db';\nimport { browserPopupRedirectResolver } from './popup_redirect';\nimport { Auth, User } from '../model/public_types';\nimport { getDefaultEmulatorHost, getExperimentalSetting } from '@firebase/util';\nimport { _setExternalJSProvider } from './load_js';\nimport { _createError } from '../core/util/assert';\nimport { AuthErrorCode } from '../core/errors';\n\nconst DEFAULT_ID_TOKEN_MAX_AGE = 5 * 60;\nconst authIdTokenMaxAge =\n  getExperimentalSetting('authIdTokenMaxAge') || DEFAULT_ID_TOKEN_MAX_AGE;\n\nlet lastPostedIdToken: string | undefined | null = null;\n\nconst mintCookieFactory = (url: string) => async (user: User | null) => {\n  const idTokenResult = user && (await user.getIdTokenResult());\n  const idTokenAge =\n    idTokenResult &&\n    (new Date().getTime() - Date.parse(idTokenResult.issuedAtTime)) / 1_000;\n  if (idTokenAge && idTokenAge > authIdTokenMaxAge) {\n    return;\n  }\n  // Specifically trip null => undefined when logged out, to delete any existing cookie\n  const idToken = idTokenResult?.token;\n  if (lastPostedIdToken === idToken) {\n    return;\n  }\n  lastPostedIdToken = idToken;\n  await fetch(url, {\n    method: idToken ? 'POST' : 'DELETE',\n    headers: idToken\n      ? {\n          'Authorization': `Bearer ${idToken}`\n        }\n      : {}\n  });\n};\n\n/**\n * Returns the Auth instance associated with the provided {@link @firebase/app#FirebaseApp}.\n * If no instance exists, initializes an Auth instance with platform-specific default dependencies.\n *\n * @param app - The Firebase App.\n *\n * @public\n */\nexport function getAuth(app: FirebaseApp = getApp()): Auth {\n  const provider = _getProvider(app, 'auth');\n\n  if (provider.isInitialized()) {\n    return provider.getImmediate();\n  }\n\n  const auth = initializeAuth(app, {\n    popupRedirectResolver: browserPopupRedirectResolver,\n    persistence: [\n      indexedDBLocalPersistence,\n      browserLocalPersistence,\n      browserSessionPersistence\n    ]\n  });\n\n  const authTokenSyncPath = getExperimentalSetting('authTokenSyncURL');\n  // Only do the Cookie exchange in a secure context\n  if (\n    authTokenSyncPath &&\n    typeof isSecureContext === 'boolean' &&\n    isSecureContext\n  ) {\n    // Don't allow urls (XSS possibility), only paths on the same domain\n    const authTokenSyncUrl = new URL(authTokenSyncPath, location.origin);\n    if (location.origin === authTokenSyncUrl.origin) {\n      const mintCookie = mintCookieFactory(authTokenSyncUrl.toString());\n      beforeAuthStateChanged(auth, mintCookie, () =>\n        mintCookie(auth.currentUser)\n      );\n      onIdTokenChanged(auth, user => mintCookie(user));\n    }\n  }\n\n  const authEmulatorHost = getDefaultEmulatorHost('auth');\n  if (authEmulatorHost) {\n    connectAuthEmulator(auth, `http://${authEmulatorHost}`);\n  }\n\n  return auth;\n}\n\nfunction getScriptParentElement(): HTMLDocument | HTMLHeadElement {\n  return document.getElementsByTagName('head')?.[0] ?? document;\n}\n\n_setExternalJSProvider({\n  loadJS(url: string): Promise<Event> {\n    // TODO: consider adding timeout support & cancellation\n    return new Promise((resolve, reject) => {\n      const el = document.createElement('script');\n      el.setAttribute('src', url);\n      el.onload = resolve;\n      el.onerror = e => {\n        const error = _createError(AuthErrorCode.INTERNAL_ERROR);\n        error.customData = e as unknown as Record<string, unknown>;\n        reject(error);\n      };\n      el.type = 'text/javascript';\n      el.charset = 'UTF-8';\n      getScriptParentElement().appendChild(el);\n    });\n  },\n\n  gapiScript: 'https://apis.google.com/js/api.js',\n  recaptchaV2Script: 'https://www.google.com/recaptcha/api.js',\n  recaptchaEnterpriseScript:\n    'https://www.google.com/recaptcha/enterprise.js?render='\n});\n\nregisterAuth(ClientPlatform.BROWSER);\n", "/**\n * @license\n * Copyright 2021 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nexport interface CordovaWindow extends Window {\n  cordova: {\n    plugins: {\n      browsertab: {\n        isAvailable(cb: (available: boolean) => void): void;\n        openUrl(url: string): void;\n        close(): void;\n      };\n    };\n\n    InAppBrowser: {\n      open(url: string, target: string, options: string): InAppBrowserRef;\n    };\n  };\n\n  universalLinks: {\n    subscribe(\n      n: null,\n      cb: (event: Record<string, string> | null) => void\n    ): void;\n  };\n\n  BuildInfo: {\n    readonly packageName: string;\n    readonly displayName: string;\n  };\n\n  handleOpenURL(url: string): void;\n}\n\nexport interface InAppBrowserRef {\n  close?: () => void;\n}\n\nexport function _cordovaWindow(): CordovaWindow {\n  return window as unknown as CordovaWindow;\n}\n", "/**\n * @license\n * Copyright 2020 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { AuthProvider } from '../../model/public_types';\nimport { AuthErrorCode } from '../../core/errors';\nimport {\n  debugAssert,\n  _assert,\n  _createError,\n  _fail\n} from '../../core/util/assert';\nimport { _isAndroid, _isIOS, _isIOS7Or8 } from '../../core/util/browser';\nimport { _getRedirectUrl } from '../../core/util/handler';\nimport { AuthInternal } from '../../model/auth';\nimport { AuthEvent } from '../../model/popup_redirect';\nimport { InAppBrowserRef, _cordovaWindow } from '../plugins';\nimport {\n  GetProjectConfigRequest,\n  _getProjectConfig\n} from '../../api/project_config/get_project_config';\n\n/**\n * How long to wait after the app comes back into focus before concluding that\n * the user closed the sign in tab.\n */\nconst REDIRECT_TIMEOUT_MS = 2000;\n\n/**\n * Generates the URL for the OAuth handler.\n */\nexport async function _generateHandlerUrl(\n  auth: AuthInternal,\n  event: AuthEvent,\n  provider: AuthProvider\n): Promise<string> {\n  // Get the cordova plugins\n  const { BuildInfo } = _cordovaWindow();\n  debugAssert(event.sessionId, 'AuthEvent did not contain a session ID');\n  const sessionDigest = await computeSha256(event.sessionId);\n\n  const additionalParams: Record<string, string> = {};\n  if (_isIOS()) {\n    // iOS app identifier\n    additionalParams['ibi'] = BuildInfo.packageName;\n  } else if (_isAndroid()) {\n    // Android app identifier\n    additionalParams['apn'] = BuildInfo.packageName;\n  } else {\n    _fail(auth, AuthErrorCode.OPERATION_NOT_SUPPORTED);\n  }\n\n  // Add the display name if available\n  if (BuildInfo.displayName) {\n    additionalParams['appDisplayName'] = BuildInfo.displayName;\n  }\n\n  // Attached the hashed session ID\n  additionalParams['sessionId'] = sessionDigest;\n  return _getRedirectUrl(\n    auth,\n    provider,\n    event.type,\n    undefined,\n    event.eventId ?? undefined,\n    additionalParams\n  );\n}\n\n/**\n * Validates that this app is valid for this project configuration\n */\nexport async function _validateOrigin(auth: AuthInternal): Promise<void> {\n  const { BuildInfo } = _cordovaWindow();\n  const request: GetProjectConfigRequest = {};\n  if (_isIOS()) {\n    request.iosBundleId = BuildInfo.packageName;\n  } else if (_isAndroid()) {\n    request.androidPackageName = BuildInfo.packageName;\n  } else {\n    _fail(auth, AuthErrorCode.OPERATION_NOT_SUPPORTED);\n  }\n\n  // Will fail automatically if package name is not authorized\n  await _getProjectConfig(auth, request);\n}\n\nexport function _performRedirect(\n  handlerUrl: string\n): Promise<InAppBrowserRef | null> {\n  // Get the cordova plugins\n  const { cordova } = _cordovaWindow();\n\n  return new Promise(resolve => {\n    cordova.plugins.browsertab.isAvailable(browserTabIsAvailable => {\n      let iabRef: InAppBrowserRef | null = null;\n      if (browserTabIsAvailable) {\n        cordova.plugins.browsertab.openUrl(handlerUrl);\n      } else {\n        // TODO: Return the inappbrowser ref that's returned from the open call\n        iabRef = cordova.InAppBrowser.open(\n          handlerUrl,\n          _isIOS7Or8() ? '_blank' : '_system',\n          'location=yes'\n        );\n      }\n      resolve(iabRef);\n    });\n  });\n}\n\n// Thin interface wrapper to avoid circular dependency with ./events module\ninterface PassiveAuthEventListener {\n  addPassiveListener(cb: () => void): void;\n  removePassiveListener(cb: () => void): void;\n}\n\n/**\n * This function waits for app activity to be seen before resolving. It does\n * this by attaching listeners to various dom events. Once the app is determined\n * to be visible, this promise resolves. AFTER that resolution, the listeners\n * are detached and any browser tabs left open will be closed.\n */\nexport async function _waitForAppResume(\n  auth: AuthInternal,\n  eventListener: PassiveAuthEventListener,\n  iabRef: InAppBrowserRef | null\n): Promise<void> {\n  // Get the cordova plugins\n  const { cordova } = _cordovaWindow();\n\n  let cleanup = (): void => {};\n  try {\n    await new Promise<void>((resolve, reject) => {\n      let onCloseTimer: number | null = null;\n\n      // DEFINE ALL THE CALLBACKS =====\n      function authEventSeen(): void {\n        // Auth event was detected. Resolve this promise and close the extra\n        // window if it's still open.\n        resolve();\n        const closeBrowserTab = cordova.plugins.browsertab?.close;\n        if (typeof closeBrowserTab === 'function') {\n          closeBrowserTab();\n        }\n        // Close inappbrowser embedded webview in iOS7 and 8 case if still\n        // open.\n        if (typeof iabRef?.close === 'function') {\n          iabRef.close();\n        }\n      }\n\n      function resumed(): void {\n        if (onCloseTimer) {\n          // This code already ran; do not rerun.\n          return;\n        }\n\n        onCloseTimer = window.setTimeout(() => {\n          // Wait two seconds after resume then reject.\n          reject(_createError(auth, AuthErrorCode.REDIRECT_CANCELLED_BY_USER));\n        }, REDIRECT_TIMEOUT_MS);\n      }\n\n      function visibilityChanged(): void {\n        if (document?.visibilityState === 'visible') {\n          resumed();\n        }\n      }\n\n      // ATTACH ALL THE LISTENERS =====\n      // Listen for the auth event\n      eventListener.addPassiveListener(authEventSeen);\n\n      // Listen for resume and visibility events\n      document.addEventListener('resume', resumed, false);\n      if (_isAndroid()) {\n        document.addEventListener('visibilitychange', visibilityChanged, false);\n      }\n\n      // SETUP THE CLEANUP FUNCTION =====\n      cleanup = () => {\n        eventListener.removePassiveListener(authEventSeen);\n        document.removeEventListener('resume', resumed, false);\n        document.removeEventListener(\n          'visibilitychange',\n          visibilityChanged,\n          false\n        );\n        if (onCloseTimer) {\n          window.clearTimeout(onCloseTimer);\n        }\n      };\n    });\n  } finally {\n    cleanup();\n  }\n}\n\n/**\n * Checks the configuration of the Cordova environment. This has no side effect\n * if the configuration is correct; otherwise it throws an error with the\n * missing plugin.\n */\nexport function _checkCordovaConfiguration(auth: AuthInternal): void {\n  const win = _cordovaWindow();\n  // Check all dependencies installed.\n  // https://github.com/nordnet/cordova-universal-links-plugin\n  // Note that cordova-universal-links-plugin has been abandoned.\n  // A fork with latest fixes is available at:\n  // https://www.npmjs.com/package/cordova-universal-links-plugin-fix\n  _assert(\n    typeof win?.universalLinks?.subscribe === 'function',\n    auth,\n    AuthErrorCode.INVALID_CORDOVA_CONFIGURATION,\n    {\n      missingPlugin: 'cordova-universal-links-plugin-fix'\n    }\n  );\n\n  // https://www.npmjs.com/package/cordova-plugin-buildinfo\n  _assert(\n    typeof win?.BuildInfo?.packageName !== 'undefined',\n    auth,\n    AuthErrorCode.INVALID_CORDOVA_CONFIGURATION,\n    {\n      missingPlugin: 'cordova-plugin-buildInfo'\n    }\n  );\n\n  // https://github.com/google/cordova-plugin-browsertab\n  _assert(\n    typeof win?.cordova?.plugins?.browsertab?.openUrl === 'function',\n    auth,\n    AuthErrorCode.INVALID_CORDOVA_CONFIGURATION,\n    {\n      missingPlugin: 'cordova-plugin-browsertab'\n    }\n  );\n  _assert(\n    typeof win?.cordova?.plugins?.browsertab?.isAvailable === 'function',\n    auth,\n    AuthErrorCode.INVALID_CORDOVA_CONFIGURATION,\n    {\n      missingPlugin: 'cordova-plugin-browsertab'\n    }\n  );\n\n  // https://cordova.apache.org/docs/en/latest/reference/cordova-plugin-inappbrowser/\n  _assert(\n    typeof win?.cordova?.InAppBrowser?.open === 'function',\n    auth,\n    AuthErrorCode.INVALID_CORDOVA_CONFIGURATION,\n    {\n      missingPlugin: 'cordova-plugin-inappbrowser'\n    }\n  );\n}\n\n/**\n * Computes the SHA-256 of a session ID. The SubtleCrypto interface is only\n * available in \"secure\" contexts, which covers Cordova (which is served on a file\n * protocol).\n */\nasync function computeSha256(sessionId: string): Promise<string> {\n  const bytes = stringToArrayBuffer(sessionId);\n\n  // TODO: For IE11 crypto has a different name and this operation comes back\n  //       as an object, not a promise. This is the old proposed standard that\n  //       is used by IE11:\n  // https://www.w3.org/TR/2013/WD-WebCryptoAPI-20130108/#cryptooperation-interface\n  const buf = await crypto.subtle.digest('SHA-256', bytes);\n  const arr = Array.from(new Uint8Array(buf));\n  return arr.map(num => num.toString(16).padStart(2, '0')).join('');\n}\n\nfunction stringToArrayBuffer(str: string): Uint8Array {\n  // This function is only meant to deal with an ASCII charset and makes\n  // certain simplifying assumptions.\n  debugAssert(\n    /[0-9a-zA-Z]+/.test(str),\n    'Can only convert alpha-numeric strings'\n  );\n  if (typeof TextEncoder !== 'undefined') {\n    return new TextEncoder().encode(str);\n  }\n\n  const buff = new ArrayBuffer(str.length);\n  const view = new Uint8Array(buff);\n  for (let i = 0; i < str.length; i++) {\n    view[i] = str.charCodeAt(i);\n  }\n  return view;\n}\n", "/**\n * @license\n * Copyright 2020 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { querystringDecode } from '@firebase/util';\nimport { AuthEventManager } from '../../core/auth/auth_event_manager';\nimport { AuthErrorCode } from '../../core/errors';\nimport { PersistedBlob, PersistenceInternal } from '../../core/persistence';\nimport {\n  KeyName,\n  _persistenceKeyName\n} from '../../core/persistence/persistence_user_manager';\nimport { _createError } from '../../core/util/assert';\nimport { _getInstance } from '../../core/util/instantiator';\nimport { AuthInternal } from '../../model/auth';\nimport { AuthEvent, AuthEventType } from '../../model/popup_redirect';\nimport { browserLocalPersistence } from '../../platform_browser/persistence/local_storage';\n\nconst SESSION_ID_LENGTH = 20;\n\n/** Custom AuthEventManager that adds passive listeners to events */\nexport class CordovaAuthEventManager extends AuthEventManager {\n  private readonly passiveListeners = new Set<(e: AuthEvent) => void>();\n  private resolveInitialized!: () => void;\n  private initPromise = new Promise<void>(resolve => {\n    this.resolveInitialized = resolve;\n  });\n\n  addPassiveListener(cb: (e: AuthEvent) => void): void {\n    this.passiveListeners.add(cb);\n  }\n\n  removePassiveListener(cb: (e: AuthEvent) => void): void {\n    this.passiveListeners.delete(cb);\n  }\n\n  // In a Cordova environment, this manager can live through multiple redirect\n  // operations\n  resetRedirect(): void {\n    this.queuedRedirectEvent = null;\n    this.hasHandledPotentialRedirect = false;\n  }\n\n  /** Override the onEvent method */\n  onEvent(event: AuthEvent): boolean {\n    this.resolveInitialized();\n    this.passiveListeners.forEach(cb => cb(event));\n    return super.onEvent(event);\n  }\n\n  async initialized(): Promise<void> {\n    await this.initPromise;\n  }\n}\n\n/**\n * Generates a (partial) {@link AuthEvent}.\n */\nexport function _generateNewEvent(\n  auth: AuthInternal,\n  type: AuthEventType,\n  eventId: string | null = null\n): AuthEvent {\n  return {\n    type,\n    eventId,\n    urlResponse: null,\n    sessionId: generateSessionId(),\n    postBody: null,\n    tenantId: auth.tenantId,\n    error: _createError(auth, AuthErrorCode.NO_AUTH_EVENT)\n  };\n}\n\nexport function _savePartialEvent(\n  auth: AuthInternal,\n  event: AuthEvent\n): Promise<void> {\n  return storage()._set(persistenceKey(auth), event as object as PersistedBlob);\n}\n\nexport async function _getAndRemoveEvent(\n  auth: AuthInternal\n): Promise<AuthEvent | null> {\n  const event = (await storage()._get(\n    persistenceKey(auth)\n  )) as AuthEvent | null;\n  if (event) {\n    await storage()._remove(persistenceKey(auth));\n  }\n  return event;\n}\n\nexport function _eventFromPartialAndUrl(\n  partialEvent: AuthEvent,\n  url: string\n): AuthEvent | null {\n  // Parse the deep link within the dynamic link URL.\n  const callbackUrl = _getDeepLinkFromCallback(url);\n  // Confirm it is actually a callback URL.\n  // Currently the universal link will be of this format:\n  // https://<AUTH_DOMAIN>/__/auth/callback<OAUTH_RESPONSE>\n  // This is a fake URL but is not intended to take the user anywhere\n  // and just redirect to the app.\n  if (callbackUrl.includes('/__/auth/callback')) {\n    // Check if there is an error in the URL.\n    // This mechanism is also used to pass errors back to the app:\n    // https://<AUTH_DOMAIN>/__/auth/callback?firebaseError=<STRINGIFIED_ERROR>\n    const params = searchParamsOrEmpty(callbackUrl);\n    // Get the error object corresponding to the stringified error if found.\n    const errorObject = params['firebaseError']\n      ? parseJsonOrNull(decodeURIComponent(params['firebaseError']))\n      : null;\n    const code = errorObject?.['code']?.split('auth/')?.[1];\n    const error = code ? _createError(code) : null;\n    if (error) {\n      return {\n        type: partialEvent.type,\n        eventId: partialEvent.eventId,\n        tenantId: partialEvent.tenantId,\n        error,\n        urlResponse: null,\n        sessionId: null,\n        postBody: null\n      };\n    } else {\n      return {\n        type: partialEvent.type,\n        eventId: partialEvent.eventId,\n        tenantId: partialEvent.tenantId,\n        sessionId: partialEvent.sessionId,\n        urlResponse: callbackUrl,\n        postBody: null\n      };\n    }\n  }\n\n  return null;\n}\n\nfunction generateSessionId(): string {\n  const chars = [];\n  const allowedChars =\n    '1234567890abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ';\n  for (let i = 0; i < SESSION_ID_LENGTH; i++) {\n    const idx = Math.floor(Math.random() * allowedChars.length);\n    chars.push(allowedChars.charAt(idx));\n  }\n  return chars.join('');\n}\n\nfunction storage(): PersistenceInternal {\n  return _getInstance(browserLocalPersistence);\n}\n\nfunction persistenceKey(auth: AuthInternal): string {\n  return _persistenceKeyName(KeyName.AUTH_EVENT, auth.config.apiKey, auth.name);\n}\n\nfunction parseJsonOrNull(json: string): ReturnType<typeof JSON.parse> | null {\n  try {\n    return JSON.parse(json);\n  } catch (e) {\n    return null;\n  }\n}\n\n// Exported for testing\nexport function _getDeepLinkFromCallback(url: string): string {\n  const params = searchParamsOrEmpty(url);\n  const link = params['link'] ? decodeURIComponent(params['link']) : undefined;\n  // Double link case (automatic redirect)\n  const doubleDeepLink = searchParamsOrEmpty(link)['link'];\n  // iOS custom scheme links.\n  const iOSDeepLink = params['deep_link_id']\n    ? decodeURIComponent(params['deep_link_id'])\n    : undefined;\n  const iOSDoubleDeepLink = searchParamsOrEmpty(iOSDeepLink)['link'];\n  return iOSDoubleDeepLink || iOSDeepLink || doubleDeepLink || link || url;\n}\n\n/**\n * Optimistically tries to get search params from a string, or else returns an\n * empty search params object.\n */\nfunction searchParamsOrEmpty(url: string | undefined): Record<string, string> {\n  if (!url?.includes('?')) {\n    return {};\n  }\n\n  const [_, ...rest] = url.split('?');\n  return querystringDecode(rest.join('?')) as Record<string, string>;\n}\n", "/**\n * @license\n * Copyright 2021 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { AuthProvider, PopupRedirectResolver } from '../../model/public_types';\nimport { browserSessionPersistence } from '../../platform_browser/persistence/session_storage';\nimport { AuthInternal } from '../../model/auth';\nimport {\n  AuthEvent,\n  AuthEventType,\n  PopupRedirectResolverInternal\n} from '../../model/popup_redirect';\nimport { AuthPopup } from '../../platform_browser/util/popup';\nimport { _createError, _fail } from '../../core/util/assert';\nimport { AuthErrorCode } from '../../core/errors';\nimport {\n  _checkCordovaConfiguration,\n  _generateHandlerUrl,\n  _performRedirect,\n  _validateOrigin,\n  _waitForAppResume\n} from './utils';\nimport {\n  CordovaAuthEventManager,\n  _eventFromPartialAndUrl,\n  _generateNewEvent,\n  _getAndRemoveEvent,\n  _savePartialEvent\n} from './events';\nimport { AuthEventManager } from '../../core/auth/auth_event_manager';\nimport { _getRedirectResult } from '../../platform_browser/strategies/redirect';\nimport {\n  _clearRedirectOutcomes,\n  _overrideRedirectResult\n} from '../../core/strategies/redirect';\nimport { _cordovaWindow } from '../plugins';\n\n/**\n * How long to wait for the initial auth event before concluding no\n * redirect pending\n */\nconst INITIAL_EVENT_TIMEOUT_MS = 500;\n\nclass CordovaPopupRedirectResolver implements PopupRedirectResolverInternal {\n  readonly _redirectPersistence = browserSessionPersistence;\n  readonly _shouldInitProactively = true; // This is lightweight for Cordova\n  private readonly eventManagers = new Map<string, CordovaAuthEventManager>();\n  private readonly originValidationPromises: Record<string, Promise<void>> = {};\n\n  _completeRedirectFn = _getRedirectResult;\n  _overrideRedirectResult = _overrideRedirectResult;\n\n  async _initialize(auth: AuthInternal): Promise<CordovaAuthEventManager> {\n    const key = auth._key();\n    let manager = this.eventManagers.get(key);\n    if (!manager) {\n      manager = new CordovaAuthEventManager(auth);\n      this.eventManagers.set(key, manager);\n      this.attachCallbackListeners(auth, manager);\n    }\n    return manager;\n  }\n\n  _openPopup(auth: AuthInternal): Promise<AuthPopup> {\n    _fail(auth, AuthErrorCode.OPERATION_NOT_SUPPORTED);\n  }\n\n  async _openRedirect(\n    auth: AuthInternal,\n    provider: AuthProvider,\n    authType: AuthEventType,\n    eventId?: string\n  ): Promise<void> {\n    _checkCordovaConfiguration(auth);\n    const manager = await this._initialize(auth);\n    await manager.initialized();\n\n    // Reset the persisted redirect states. This does not matter on Web where\n    // the redirect always blows away application state entirely. On Cordova,\n    // the app maintains control flow through the redirect.\n    manager.resetRedirect();\n    _clearRedirectOutcomes();\n\n    await this._originValidation(auth);\n\n    const event = _generateNewEvent(auth, authType, eventId);\n    await _savePartialEvent(auth, event);\n    const url = await _generateHandlerUrl(auth, event, provider);\n    const iabRef = await _performRedirect(url);\n    return _waitForAppResume(auth, manager, iabRef);\n  }\n\n  _isIframeWebStorageSupported(\n    _auth: AuthInternal,\n    _cb: (support: boolean) => unknown\n  ): void {\n    throw new Error('Method not implemented.');\n  }\n\n  _originValidation(auth: AuthInternal): Promise<void> {\n    const key = auth._key();\n    if (!this.originValidationPromises[key]) {\n      this.originValidationPromises[key] = _validateOrigin(auth);\n    }\n\n    return this.originValidationPromises[key];\n  }\n\n  private attachCallbackListeners(\n    auth: AuthInternal,\n    manager: AuthEventManager\n  ): void {\n    // Get the global plugins\n    const { universalLinks, handleOpenURL, BuildInfo } = _cordovaWindow();\n\n    const noEventTimeout = setTimeout(async () => {\n      // We didn't see that initial event. Clear any pending object and\n      // dispatch no event\n      await _getAndRemoveEvent(auth);\n      manager.onEvent(generateNoEvent());\n    }, INITIAL_EVENT_TIMEOUT_MS);\n\n    const universalLinksCb = async (\n      eventData: Record<string, string> | null\n    ): Promise<void> => {\n      // We have an event so we can clear the no event timeout\n      clearTimeout(noEventTimeout);\n\n      const partialEvent = await _getAndRemoveEvent(auth);\n      let finalEvent: AuthEvent | null = null;\n      if (partialEvent && eventData?.['url']) {\n        finalEvent = _eventFromPartialAndUrl(partialEvent, eventData['url']);\n      }\n\n      // If finalEvent is never filled, trigger with no event\n      manager.onEvent(finalEvent || generateNoEvent());\n    };\n\n    // Universal links subscriber doesn't exist for iOS, so we need to check\n    if (\n      typeof universalLinks !== 'undefined' &&\n      typeof universalLinks.subscribe === 'function'\n    ) {\n      universalLinks.subscribe(null, universalLinksCb);\n    }\n\n    // iOS 7 or 8 custom URL schemes.\n    // This is also the current default behavior for iOS 9+.\n    // For this to work, cordova-plugin-customurlscheme needs to be installed.\n    // https://github.com/EddyVerbruggen/Custom-URL-scheme\n    // Do not overwrite the existing developer's URL handler.\n    const existingHandleOpenURL = handleOpenURL;\n    const packagePrefix = `${BuildInfo.packageName.toLowerCase()}://`;\n    _cordovaWindow().handleOpenURL = async url => {\n      if (url.toLowerCase().startsWith(packagePrefix)) {\n        // We want this intentionally to float\n        // eslint-disable-next-line @typescript-eslint/no-floating-promises\n        universalLinksCb({ url });\n      }\n      // Call the developer's handler if it is present.\n      if (typeof existingHandleOpenURL === 'function') {\n        try {\n          existingHandleOpenURL(url);\n        } catch (e) {\n          // This is a developer error. Don't stop the flow of the SDK.\n          console.error(e);\n        }\n      }\n    };\n  }\n}\n\n/**\n * An implementation of {@link PopupRedirectResolver} suitable for Cordova\n * based applications.\n *\n * @public\n */\nexport const cordovaPopupRedirectResolver: PopupRedirectResolver =\n  CordovaPopupRedirectResolver;\n\nfunction generateNoEvent(): AuthEvent {\n  return {\n    type: AuthEventType.UNKNOWN,\n    eventId: null,\n    sessionId: null,\n    urlResponse: null,\n    postBody: null,\n    tenantId: null,\n    error: _createError(AuthErrorCode.NO_AUTH_EVENT)\n  };\n}\n", "/**\n * @license\n * Copyright 2017 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { _castAuth } from '../src/core/auth/auth_impl';\nimport { Auth } from '../src/model/public_types';\n\n/**\n * This interface is intended only for use by @firebase/auth-compat, do not use directly\n */\nexport * from '../index';\n\nexport { SignInWithIdpResponse } from '../src/api/authentication/idp';\nexport { AuthErrorCode } from '../src/core/errors';\nexport { PersistenceInternal } from '../src/core/persistence';\nexport { _persistenceKeyName } from '../src/core/persistence/persistence_user_manager';\nexport { UserImpl } from '../src/core/user/user_impl';\nexport { _getInstance } from '../src/core/util/instantiator';\nexport {\n  PopupRedirectResolverInternal,\n  EventManager,\n  AuthEventType\n} from '../src/model/popup_redirect';\nexport { UserCredentialInternal, UserParameters } from '../src/model/user';\nexport { AuthInternal, ConfigInternal } from '../src/model/auth';\nexport { DefaultConfig, AuthImpl, _castAuth } from '../src/core/auth/auth_impl';\n\nexport { ClientPlatform, _getClientVersion } from '../src/core/util/version';\n\nexport { _generateEventId } from '../src/core/util/event_id';\nexport { TaggedWithTokenResponse } from '../src/model/id_token';\nexport { _fail, _assert } from '../src/core/util/assert';\nexport { AuthPopup } from '../src/platform_browser/util/popup';\nexport { _getRedirectResult } from '../src/platform_browser/strategies/redirect';\nexport { _overrideRedirectResult } from '../src/core/strategies/redirect';\nexport { cordovaPopupRedirectResolver } from '../src/platform_cordova/popup_redirect/popup_redirect';\nexport { FetchProvider } from '../src/core/util/fetch_provider';\nexport { SAMLAuthCredential } from '../src/core/credentials/saml';\n\n// This function should only be called by frameworks (e.g. FirebaseUI-web) to log their usage.\n// It is not intended for direct use by developer apps. NO jsdoc here to intentionally leave it out\n// of autogenerated documentation pages to reduce accidental misuse.\nexport function addFrameworkForLogging(auth: Auth, framework: string): void {\n  _castAuth(auth)._logFramework(framework);\n}\n"], "names": ["STORAGE_AVAILABLE_KEY", "_POLLING_INTERVAL_MS", "__extends", "_isMobileBrowser", "_isIE10", "__awaiter", "__generator", "_isWorker", "_getWorkerGlobalScope", "_getActiveServiceWorker", "_getServiceWorkerController", "_getInstance", "_assert", "signInWithIdp", "AuthCredential", "_signInWithCredential", "_reauthenticate", "_linkUser", "_fail", "debugAssert", "Delay", "_isFirebaseServerApp", "_createError", "_castAuth", "_assertInstanceOf", "FederatedAuthProvider", "getModularInstance", "_persistenceKeyName", "_serverAppCurrentUserOperationNotSupportedError", "_assertLinkedStatus", "_performApiRequest", "_validate<PERSON><PERSON><PERSON>", "_getCurrentUrl", "_window", "__spread<PERSON><PERSON>y", "js._generateCallbackName", "js\r\n                ._loadJS", "js._gapiScriptUrl", "_emulatorUrl", "SDK_VERSION", "querystring", "gapiLoader._loadGapi", "__assign", "getUA", "_isChromeIOS", "_isFirefox", "_isIOSStandalone", "isEmpty", "BaseOAuthProvider", "_setWindowLocation", "_is<PERSON><PERSON><PERSON>", "_isIOS", "getExperimentalSetting", "app", "getApp", "_get<PERSON><PERSON><PERSON>", "initializeAuth", "beforeAuthStateChanged", "onIdTokenChanged", "getDefaultEmulatorHost", "connectAuthEmulator", "_setExternalJSProvider", "registerAuth", "_isAndroid", "_isIOS7Or8", "querystringDecode"], "mappings": ";;;;;;;;;;;AAAA;;;;;;;;;;;;;;;AAeG;AAQH;AACA;AACA;AAEA,IAAA,uBAAA,kBAAA,YAAA;IACE,SACqB,uBAAA,CAAA,gBAA+B,EACzC,IAAqB,EAAA;QADX,IAAgB,CAAA,gBAAA,GAAhB,gBAAgB,CAAe;QACzC,IAAI,CAAA,IAAA,GAAJ,IAAI,CAAiB;KAC5B;AAEJ,IAAA,uBAAA,CAAA,SAAA,CAAA,YAAY,GAAZ,YAAA;QACE,IAAI;AACF,YAAA,IAAI,CAAC,IAAI,CAAC,OAAO,EAAE;AACjB,gBAAA,OAAO,OAAO,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC;AAC/B,aAAA;YACD,IAAI,CAAC,OAAO,CAAC,OAAO,CAACA,2BAAqB,EAAE,GAAG,CAAC,CAAC;AACjD,YAAA,IAAI,CAAC,OAAO,CAAC,UAAU,CAACA,2BAAqB,CAAC,CAAC;AAC/C,YAAA,OAAO,OAAO,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC;AAC9B,SAAA;QAAC,OAAM,EAAA,EAAA;AACN,YAAA,OAAO,OAAO,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC;AAC/B,SAAA;KACF,CAAA;AAED,IAAA,uBAAA,CAAA,SAAA,CAAA,IAAI,GAAJ,UAAK,GAAW,EAAE,KAAuB,EAAA;AACvC,QAAA,IAAI,CAAC,OAAO,CAAC,OAAO,CAAC,GAAG,EAAE,IAAI,CAAC,SAAS,CAAC,KAAK,CAAC,CAAC,CAAC;AACjD,QAAA,OAAO,OAAO,CAAC,OAAO,EAAE,CAAC;KAC1B,CAAA;IAED,uBAAI,CAAA,SAAA,CAAA,IAAA,GAAJ,UAAiC,GAAW,EAAA;QAC1C,IAAM,IAAI,GAAG,IAAI,CAAC,OAAO,CAAC,OAAO,CAAC,GAAG,CAAC,CAAC;AACvC,QAAA,OAAO,OAAO,CAAC,OAAO,CAAC,IAAI,GAAG,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,GAAG,IAAI,CAAC,CAAC;KACxD,CAAA;IAED,uBAAO,CAAA,SAAA,CAAA,OAAA,GAAP,UAAQ,GAAW,EAAA;AACjB,QAAA,IAAI,CAAC,OAAO,CAAC,UAAU,CAAC,GAAG,CAAC,CAAC;AAC7B,QAAA,OAAO,OAAO,CAAC,OAAO,EAAE,CAAC;KAC1B,CAAA;AAED,IAAA,MAAA,CAAA,cAAA,CAAc,uBAAO,CAAA,SAAA,EAAA,SAAA,EAAA;AAArB,QAAA,GAAA,EAAA,YAAA;AACE,YAAA,OAAO,IAAI,CAAC,gBAAgB,EAAE,CAAC;SAChC;;;AAAA,KAAA,CAAA,CAAA;IACH,OAAC,uBAAA,CAAA;AAAD,CAAC,EAAA,CAAA;;AChED;;;;;;;;;;;;;;;AAeG;AAaH;AACO,IAAMC,sBAAoB,GAAG,IAAI,CAAC;AAEzC;AACA,IAAM,6BAA6B,GAAG,EAAE,CAAC;AAEzC,IAAA,uBAAA,kBAAA,UAAA,MAAA,EAAA;IACUC,eAAuB,CAAA,uBAAA,EAAA,MAAA,CAAA,CAAA;AAK/B,IAAA,SAAA,uBAAA,GAAA;QAAA,IACE,KAAA,GAAA,MAAA,CAAA,IAAA,CAAA,IAAA,EAAM,cAAM,OAAA,MAAM,CAAC,YAAY,CAAA,EAAA,EAAA,OAAA,6BAAwB,IACxD,IAAA,CAAA;AAEgB,QAAA,KAAA,CAAA,iBAAiB,GAAG,UACnC,KAAmB,EACnB,IAAc,IACL,OAAA,KAAI,CAAC,cAAc,CAAC,KAAK,EAAE,IAAI,CAAC,CAAA,EAAA,CAAC;QAC3B,KAAS,CAAA,SAAA,GAA8C,EAAE,CAAC;QAC1D,KAAU,CAAA,UAAA,GAAkC,EAAE,CAAC;;;QAGxD,KAAS,CAAA,SAAA,GAAe,IAAI,CAAC;;QAGpB,KAAiB,CAAA,iBAAA,GAAGC,sBAAgB,EAAE,CAAC;QAC/C,KAAqB,CAAA,qBAAA,GAAG,IAAI,CAAC;;KAdrC;IAgBO,uBAAiB,CAAA,SAAA,CAAA,iBAAA,GAAzB,UACE,EAA2E,EAAA;;AAG3E,QAAA,KAAkB,IAA2B,EAAA,GAAA,CAAA,EAA3B,EAAA,GAAA,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,SAAS,CAAC,EAA3B,EAA2B,GAAA,EAAA,CAAA,MAAA,EAA3B,IAA2B,EAAE;AAA1C,YAAA,IAAM,GAAG,GAAA,EAAA,CAAA,EAAA,CAAA,CAAA;;YAEZ,IAAM,QAAQ,GAAG,IAAI,CAAC,OAAO,CAAC,OAAO,CAAC,GAAG,CAAC,CAAC;YAC3C,IAAM,QAAQ,GAAG,IAAI,CAAC,UAAU,CAAC,GAAG,CAAC,CAAC;;;YAGtC,IAAI,QAAQ,KAAK,QAAQ,EAAE;AACzB,gBAAA,EAAE,CAAC,GAAG,EAAE,QAAQ,EAAE,QAAQ,CAAC,CAAC;AAC7B,aAAA;AACF,SAAA;KACF,CAAA;AAEO,IAAA,uBAAA,CAAA,SAAA,CAAA,cAAc,GAAtB,UAAuB,KAAmB,EAAE,IAAY,EAAA;QAAxD,IAmDC,KAAA,GAAA,IAAA,CAAA;AAnD2C,QAAA,IAAA,IAAA,KAAA,KAAA,CAAA,EAAA,EAAA,IAAY,GAAA,KAAA,CAAA,EAAA;;AAEtD,QAAA,IAAI,CAAC,KAAK,CAAC,GAAG,EAAE;YACd,IAAI,CAAC,iBAAiB,CACpB,UAAC,GAAW,EAAE,SAAwB,EAAE,QAAuB,EAAA;AAC7D,gBAAA,KAAI,CAAC,eAAe,CAAC,GAAG,EAAE,QAAQ,CAAC,CAAC;AACtC,aAAC,CACF,CAAC;YACF,OAAO;AACR,SAAA;AAED,QAAA,IAAM,GAAG,GAAG,KAAK,CAAC,GAAG,CAAC;;;AAItB,QAAA,IAAI,IAAI,EAAE;;;YAGR,IAAI,CAAC,cAAc,EAAE,CAAC;AACvB,SAAA;AAAM,aAAA;;;YAGL,IAAI,CAAC,WAAW,EAAE,CAAC;AACpB,SAAA;AAED,QAAA,IAAM,gBAAgB,GAAG,YAAA;;;YAGvB,IAAM,WAAW,GAAG,KAAI,CAAC,OAAO,CAAC,OAAO,CAAC,GAAG,CAAC,CAAC;YAC9C,IAAI,CAAC,IAAI,IAAI,KAAI,CAAC,UAAU,CAAC,GAAG,CAAC,KAAK,WAAW,EAAE;;;gBAGjD,OAAO;AACR,aAAA;AACD,YAAA,KAAI,CAAC,eAAe,CAAC,GAAG,EAAE,WAAW,CAAC,CAAC;AACzC,SAAC,CAAC;QAEF,IAAM,WAAW,GAAG,IAAI,CAAC,OAAO,CAAC,OAAO,CAAC,GAAG,CAAC,CAAC;AAC9C,QAAA,IACEC,aAAO,EAAE;YACT,WAAW,KAAK,KAAK,CAAC,QAAQ;AAC9B,YAAA,KAAK,CAAC,QAAQ,KAAK,KAAK,CAAC,QAAQ,EACjC;;;;;AAKA,YAAA,UAAU,CAAC,gBAAgB,EAAE,6BAA6B,CAAC,CAAC;AAC7D,SAAA;AAAM,aAAA;AACL,YAAA,gBAAgB,EAAE,CAAC;AACpB,SAAA;KACF,CAAA;AAEO,IAAA,uBAAA,CAAA,SAAA,CAAA,eAAe,GAAvB,UAAwB,GAAW,EAAE,KAAoB,EAAA;AACvD,QAAA,IAAI,CAAC,UAAU,CAAC,GAAG,CAAC,GAAG,KAAK,CAAC;QAC7B,IAAM,SAAS,GAAG,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC,CAAC;AACtC,QAAA,IAAI,SAAS,EAAE;AACb,YAAA,KAAuB,IAAqB,EAAA,GAAA,CAAA,EAArB,EAAA,GAAA,KAAK,CAAC,IAAI,CAAC,SAAS,CAAC,EAArB,EAAqB,GAAA,EAAA,CAAA,MAAA,EAArB,IAAqB,EAAE;AAAzC,gBAAA,IAAM,QAAQ,GAAA,EAAA,CAAA,EAAA,CAAA,CAAA;AACjB,gBAAA,QAAQ,CAAC,KAAK,GAAG,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,GAAG,KAAK,CAAC,CAAC;AAC7C,aAAA;AACF,SAAA;KACF,CAAA;AAEO,IAAA,uBAAA,CAAA,SAAA,CAAA,YAAY,GAApB,YAAA;QAAA,IAiBC,KAAA,GAAA,IAAA,CAAA;QAhBC,IAAI,CAAC,WAAW,EAAE,CAAC;AAEnB,QAAA,IAAI,CAAC,SAAS,GAAG,WAAW,CAAC,YAAA;YAC3B,KAAI,CAAC,iBAAiB,CACpB,UAAC,GAAW,EAAE,QAAuB,EAAE,QAAuB,EAAA;AAC5D,gBAAA,KAAI,CAAC,cAAc,CACjB,IAAI,YAAY,CAAC,SAAS,EAAE;AAC1B,oBAAA,GAAG,EAAA,GAAA;AACH,oBAAA,QAAQ,EAAA,QAAA;AACR,oBAAA,QAAQ,EAAA,QAAA;iBACT,CAAC;2BACS,IAAI,CAChB,CAAC;AACJ,aAAC,CACF,CAAC;SACH,EAAEH,sBAAoB,CAAC,CAAC;KAC1B,CAAA;AAEO,IAAA,uBAAA,CAAA,SAAA,CAAA,WAAW,GAAnB,YAAA;QACE,IAAI,IAAI,CAAC,SAAS,EAAE;AAClB,YAAA,aAAa,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;AAC9B,YAAA,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC;AACvB,SAAA;KACF,CAAA;AAEO,IAAA,uBAAA,CAAA,SAAA,CAAA,cAAc,GAAtB,YAAA;QACE,MAAM,CAAC,gBAAgB,CAAC,SAAS,EAAE,IAAI,CAAC,iBAAiB,CAAC,CAAC;KAC5D,CAAA;AAEO,IAAA,uBAAA,CAAA,SAAA,CAAA,cAAc,GAAtB,YAAA;QACE,MAAM,CAAC,mBAAmB,CAAC,SAAS,EAAE,IAAI,CAAC,iBAAiB,CAAC,CAAC;KAC/D,CAAA;AAED,IAAA,uBAAA,CAAA,SAAA,CAAA,YAAY,GAAZ,UAAa,GAAW,EAAE,QAA8B,EAAA;AACtD,QAAA,IAAI,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC,MAAM,KAAK,CAAC,EAAE;;;;;YAK5C,IAAI,IAAI,CAAC,iBAAiB,EAAE;gBAC1B,IAAI,CAAC,YAAY,EAAE,CAAC;AACrB,aAAA;AAAM,iBAAA;gBACL,IAAI,CAAC,cAAc,EAAE,CAAC;AACvB,aAAA;AACF,SAAA;AACD,QAAA,IAAI,CAAC,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC,EAAE;YACxB,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC,GAAG,IAAI,GAAG,EAAE,CAAC;;AAEhC,YAAA,IAAI,CAAC,UAAU,CAAC,GAAG,CAAC,GAAG,IAAI,CAAC,OAAO,CAAC,OAAO,CAAC,GAAG,CAAC,CAAC;AAClD,SAAA;QACD,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC,QAAQ,CAAC,CAAC;KACnC,CAAA;AAED,IAAA,uBAAA,CAAA,SAAA,CAAA,eAAe,GAAf,UAAgB,GAAW,EAAE,QAA8B,EAAA;AACzD,QAAA,IAAI,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC,EAAE;YACvB,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC,CAAC,MAAM,CAAC,QAAQ,CAAC,CAAC;YAErC,IAAI,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC,CAAC,IAAI,KAAK,CAAC,EAAE;AAClC,gBAAA,OAAO,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC,CAAC;AAC5B,aAAA;AACF,SAAA;AAED,QAAA,IAAI,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC,MAAM,KAAK,CAAC,EAAE;YAC5C,IAAI,CAAC,cAAc,EAAE,CAAC;YACtB,IAAI,CAAC,WAAW,EAAE,CAAC;AACpB,SAAA;KACF,CAAA;;AAIK,IAAA,uBAAA,CAAA,SAAA,CAAA,IAAI,GAAV,UAAW,GAAW,EAAE,KAAuB,EAAA;;;;AAC7C,oBAAA,KAAA,CAAA,EAAA,OAAA,CAAA,CAAA,YAAM,iBAAM,IAAI,CAAA,IAAA,CAAA,IAAA,EAAC,GAAG,EAAE,KAAK,CAAC,CAAA,CAAA;;AAA5B,wBAAA,EAAA,CAAA,IAAA,EAA4B,CAAC;AAC7B,wBAAA,IAAI,CAAC,UAAU,CAAC,GAAG,CAAC,GAAG,IAAI,CAAC,SAAS,CAAC,KAAK,CAAC,CAAC;;;;;AAC9C,KAAA,CAAA;IAEK,uBAAI,CAAA,SAAA,CAAA,IAAA,GAAV,UAAuC,GAAW,EAAA;;;;;AAClC,oBAAA,KAAA,CAAA,EAAA,OAAA,CAAA,CAAA,YAAM,MAAM,CAAA,SAAA,CAAA,IAAI,CAAI,IAAA,CAAA,IAAA,EAAA,GAAG,CAAC,CAAA,CAAA;;AAAhC,wBAAA,KAAK,GAAG,EAAwB,CAAA,IAAA,EAAA,CAAA;AACtC,wBAAA,IAAI,CAAC,UAAU,CAAC,GAAG,CAAC,GAAG,IAAI,CAAC,SAAS,CAAC,KAAK,CAAC,CAAC;AAC7C,wBAAA,OAAA,CAAA,CAAA,aAAO,KAAK,CAAC,CAAA;;;;AACd,KAAA,CAAA;IAEK,uBAAO,CAAA,SAAA,CAAA,OAAA,GAAb,UAAc,GAAW,EAAA;;;;AACvB,oBAAA,KAAA,CAAA,EAAA,OAAA,CAAA,CAAA,YAAM,MAAM,CAAA,SAAA,CAAA,OAAO,CAAC,IAAA,CAAA,IAAA,EAAA,GAAG,CAAC,CAAA,CAAA;;AAAxB,wBAAA,EAAA,CAAA,IAAA,EAAwB,CAAC;AACzB,wBAAA,OAAO,IAAI,CAAC,UAAU,CAAC,GAAG,CAAC,CAAC;;;;;AAC7B,KAAA,CAAA;IAxLM,uBAAI,CAAA,IAAA,GAAY,OAAO,CAAC;IAyLjC,OAAC,uBAAA,CAAA;CAAA,CA5LS,uBAAuB,CA4LhC,CAAA,CAAA;AAED;;;;;AAKG;AACI,IAAM,uBAAuB,GAAgB;;ACvOpD;;;;;;;;;;;;;;;AAeG;AAWH,IAAA,yBAAA,kBAAA,UAAA,MAAA,EAAA;IACUC,eAAuB,CAAA,yBAAA,EAAA,MAAA,CAAA,CAAA;AAK/B,IAAA,SAAA,yBAAA,GAAA;AACE,QAAA,OAAA,MAAA,CAAA,IAAA,CAAA,IAAA,EAAM,cAAM,OAAA,MAAM,CAAC,cAAc,CAAA,EAAA,EAA0B,SAAA,+BAAA,IAAA,IAAA,CAAA;KAC5D;AAED,IAAA,yBAAA,CAAA,SAAA,CAAA,YAAY,GAAZ,UAAa,IAAY,EAAE,SAA+B,EAAA;;QAExD,OAAO;KACR,CAAA;AAED,IAAA,yBAAA,CAAA,SAAA,CAAA,eAAe,GAAf,UAAgB,IAAY,EAAE,SAA+B,EAAA;;QAE3D,OAAO;KACR,CAAA;IAdM,yBAAI,CAAA,IAAA,GAAc,SAAS,CAAC;IAerC,OAAC,yBAAA,CAAA;CAAA,CAlBS,uBAAuB,CAkBhC,CAAA,CAAA;AAED;;;;;AAKG;AACI,IAAM,yBAAyB,GAAgB;;ACrDtD;;;;;;;;;;;;;;;AAeG;AAkBH;;;;AAIG;AACG,SAAU,WAAW,CACzB,QAA2B,EAAA;IAD7B,IAmBC,KAAA,GAAA,IAAA,CAAA;IAhBC,OAAO,OAAO,CAAC,GAAG,CAChB,QAAQ,CAAC,GAAG,CAAC,UAAM,OAAO,EAAA,EAAA,OAAAG,eAAA,CAAA,KAAA,EAAA,KAAA,CAAA,EAAA,KAAA,CAAA,EAAA,YAAA;;;;;;AAER,oBAAA,OAAA,CAAA,CAAA,YAAM,OAAO,CAAA,CAAA;;AAArB,oBAAA,KAAK,GAAG,EAAa,CAAA,IAAA,EAAA,CAAA;oBAC3B,OAAO,CAAA,CAAA,aAAA;AACL,4BAAA,SAAS,EAAE,IAAI;AACf,4BAAA,KAAK,EAAA,KAAA;yBACuB,CAAC,CAAA;;;oBAE/B,OAAO,CAAA,CAAA,aAAA;AACL,4BAAA,SAAS,EAAE,KAAK;AAChB,4BAAA,MAAM,EAAA,QAAA;yBACkB,CAAC,CAAA;;;;AAE9B,KAAA,CAAA,CAAA,EAAA,CAAC,CACH,CAAC;AACJ;;ACzDA;;;;;;;;;;;;;;;AAeG;AAYH;;;AAGG;AACH,IAAA,QAAA,kBAAA,YAAA;AAUE,IAAA,SAAA,QAAA,CAA6B,WAAwB,EAAA;QAAxB,IAAW,CAAA,WAAA,GAAX,WAAW,CAAa;QANpC,IAAW,CAAA,WAAA,GAIxB,EAAE,CAAC;QAGL,IAAI,CAAC,iBAAiB,GAAG,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;KACtD;AAED;;;;;AAKG;IACI,QAAY,CAAA,YAAA,GAAnB,UAAoB,WAAwB,EAAA;;;;QAI1C,IAAM,gBAAgB,GAAG,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,UAAA,QAAQ,EAAA;AACnD,YAAA,OAAA,QAAQ,CAAC,aAAa,CAAC,WAAW,CAAC,CAAA;AAAnC,SAAmC,CACpC,CAAC;AACF,QAAA,IAAI,gBAAgB,EAAE;AACpB,YAAA,OAAO,gBAAgB,CAAC;AACzB,SAAA;AACD,QAAA,IAAM,WAAW,GAAG,IAAI,QAAQ,CAAC,WAAW,CAAC,CAAC;AAC9C,QAAA,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC;AACjC,QAAA,OAAO,WAAW,CAAC;KACpB,CAAA;IAEO,QAAa,CAAA,SAAA,CAAA,aAAA,GAArB,UAAsB,WAAwB,EAAA;AAC5C,QAAA,OAAO,IAAI,CAAC,WAAW,KAAK,WAAW,CAAC;KACzC,CAAA;AAED;;;;;;;;;AASG;IACW,QAAW,CAAA,SAAA,CAAA,WAAA,GAAzB,UAGE,KAAY,EAAA;;;;;;;wBACN,YAAY,GAAG,KAA4C,CAAC;AAC5D,wBAAA,EAAA,GAA+B,YAAY,CAAC,IAAI,EAA9C,OAAO,GAAA,EAAA,CAAA,OAAA,EAAE,SAAS,GAAA,EAAA,CAAA,SAAA,EAAE,IAAI,GAAA,EAAA,CAAA,IAAA,CAAuB;AAEjD,wBAAA,QAAQ,GACZ,IAAI,CAAC,WAAW,CAAC,SAAS,CAAC,CAAC;wBAC9B,IAAI,EAAC,QAAQ,KAAR,IAAA,IAAA,QAAQ,uBAAR,QAAQ,CAAE,IAAI,CAAA,EAAE;4BACnB,OAAO,CAAA,CAAA,YAAA,CAAA;AACR,yBAAA;AAED,wBAAA,YAAY,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,WAAW,CAAC;AAChC,4BAAA,MAAM,EAAa,KAAA;AACnB,4BAAA,OAAO,EAAA,OAAA;AACP,4BAAA,SAAS,EAAA,SAAA;AACV,yBAAA,CAAC,CAAC;wBAEG,QAAQ,GAAG,KAAK,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC,GAAG,CAAC,UAAM,OAAO,EAAA,EAAA,OAAAA,eAAA,CAAA,KAAA,EAAA,KAAA,CAAA,EAAA,KAAA,CAAA,EAAA,YAAA,EAAA,OAAAC,iBAAA,CAAA,IAAA,EAAA,UAAA,EAAA,EAAA;4BACrD,OAAA,CAAA,CAAA,aAAA,OAAO,CAAC,YAAY,CAAC,MAAM,EAAE,IAAI,CAAC,CAAA,CAAA;AAAA,yBAAA,CAAA,CAAA,EAAA,CAAA,CAAA,EAAA,CACnC,CAAC;AACe,wBAAA,OAAA,CAAA,CAAA,YAAM,WAAW,CAAC,QAAQ,CAAC,CAAA,CAAA;;AAAtC,wBAAA,QAAQ,GAAG,EAA2B,CAAA,IAAA,EAAA,CAAA;AAC5C,wBAAA,YAAY,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,WAAW,CAAC;AAChC,4BAAA,MAAM,EAAc,MAAA;AACpB,4BAAA,OAAO,EAAA,OAAA;AACP,4BAAA,SAAS,EAAA,SAAA;AACT,4BAAA,QAAQ,EAAA,QAAA;AACT,yBAAA,CAAC,CAAC;;;;;AACJ,KAAA,CAAA;AAED;;;;;;AAMG;AACH,IAAA,QAAA,CAAA,SAAA,CAAA,UAAU,GAAV,UACE,SAAqB,EACrB,YAAmC,EAAA;AAEnC,QAAA,IAAI,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC,MAAM,KAAK,CAAC,EAAE;YAC9C,IAAI,CAAC,WAAW,CAAC,gBAAgB,CAAC,SAAS,EAAE,IAAI,CAAC,iBAAiB,CAAC,CAAC;AACtE,SAAA;AAED,QAAA,IAAI,CAAC,IAAI,CAAC,WAAW,CAAC,SAAS,CAAC,EAAE;YAChC,IAAI,CAAC,WAAW,CAAC,SAAS,CAAC,GAAG,IAAI,GAAG,EAAE,CAAC;AACzC,SAAA;QAED,IAAI,CAAC,WAAW,CAAC,SAAS,CAAC,CAAC,GAAG,CAAC,YAAY,CAAC,CAAC;KAC/C,CAAA;AAED;;;;;;AAMG;AACH,IAAA,QAAA,CAAA,SAAA,CAAA,YAAY,GAAZ,UACE,SAAqB,EACrB,YAAoC,EAAA;QAEpC,IAAI,IAAI,CAAC,WAAW,CAAC,SAAS,CAAC,IAAI,YAAY,EAAE;YAC/C,IAAI,CAAC,WAAW,CAAC,SAAS,CAAC,CAAC,MAAM,CAAC,YAAY,CAAC,CAAC;AAClD,SAAA;AACD,QAAA,IAAI,CAAC,YAAY,IAAI,IAAI,CAAC,WAAW,CAAC,SAAS,CAAC,CAAC,IAAI,KAAK,CAAC,EAAE;AAC3D,YAAA,OAAO,IAAI,CAAC,WAAW,CAAC,SAAS,CAAC,CAAC;AACpC,SAAA;AAED,QAAA,IAAI,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC,MAAM,KAAK,CAAC,EAAE;YAC9C,IAAI,CAAC,WAAW,CAAC,mBAAmB,CAAC,SAAS,EAAE,IAAI,CAAC,iBAAiB,CAAC,CAAC;AACzE,SAAA;KACF,CAAA;IA1HuB,QAAS,CAAA,SAAA,GAAe,EAAE,CAAC;IA2HrD,OAAC,QAAA,CAAA;AAAA,CA5HD,EA4HC,CAAA;;AC3JD;;;;;;;;;;;;;;;AAeG;AAEa,SAAA,gBAAgB,CAAC,MAAW,EAAE,MAAW,EAAA;AAAxB,IAAA,IAAA,MAAA,KAAA,KAAA,CAAA,EAAA,EAAA,MAAW,GAAA,EAAA,CAAA,EAAA;AAAE,IAAA,IAAA,MAAA,KAAA,KAAA,CAAA,EAAA,EAAA,MAAW,GAAA,EAAA,CAAA,EAAA;IACvD,IAAI,MAAM,GAAG,EAAE,CAAC;IAChB,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,MAAM,EAAE,CAAC,EAAE,EAAE;AAC/B,QAAA,MAAM,IAAI,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,MAAM,EAAE,GAAG,EAAE,CAAC,CAAC;AAC1C,KAAA;IACD,OAAO,MAAM,GAAG,MAAM,CAAC;AACzB;;ACvBA;;;;;;;;;;;;;;;AAeG;AAoBH;;;AAGG;AACH,IAAA,MAAA,kBAAA,YAAA;AAGE,IAAA,SAAA,MAAA,CAA6B,MAAqB,EAAA;QAArB,IAAM,CAAA,MAAA,GAAN,MAAM,CAAe;AAFjC,QAAA,IAAA,CAAA,QAAQ,GAAG,IAAI,GAAG,EAAkB,CAAC;KAEA;AAEtD;;;;AAIG;IACK,MAAoB,CAAA,SAAA,CAAA,oBAAA,GAA5B,UAA6B,OAAuB,EAAA;QAClD,IAAI,OAAO,CAAC,cAAc,EAAE;AAC1B,YAAA,OAAO,CAAC,cAAc,CAAC,KAAK,CAAC,mBAAmB,CAC9C,SAAS,EACT,OAAO,CAAC,SAAS,CAClB,CAAC;AACF,YAAA,OAAO,CAAC,cAAc,CAAC,KAAK,CAAC,KAAK,EAAE,CAAC;AACtC,SAAA;AACD,QAAA,IAAI,CAAC,QAAQ,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC;KAC/B,CAAA;AAED;;;;;;;;;;;;AAYG;AACG,IAAA,MAAA,CAAA,SAAA,CAAA,KAAK,GAAX,UACE,SAAqB,EACrB,IAAO,EACP,OAA8B,EAAA;AAA9B,QAAA,IAAA,OAAA,KAAA,KAAA,CAAA,EAAA,EAAA,OAA8B,GAAA,EAAA,4BAAA,EAAA;;;;;AAExB,gBAAA,cAAc,GAClB,OAAO,cAAc,KAAK,WAAW,GAAG,IAAI,cAAc,EAAE,GAAG,IAAI,CAAC;gBACtE,IAAI,CAAC,cAAc,EAAE;oBACnB,MAAM,IAAI,KAAK,CAAA,wBAAA,4CAAsC,CAAC;AACvD,iBAAA;AAOD,gBAAA,OAAA,CAAA,CAAA,aAAO,IAAI,OAAO,CAA8B,UAAC,OAAO,EAAE,MAAM,EAAA;wBAC9D,IAAM,OAAO,GAAG,gBAAgB,CAAC,EAAE,EAAE,EAAE,CAAC,CAAC;AACzC,wBAAA,cAAc,CAAC,KAAK,CAAC,KAAK,EAAE,CAAC;wBAC7B,IAAM,QAAQ,GAAG,UAAU,CAAC,YAAA;AAC1B,4BAAA,MAAM,CAAC,IAAI,KAAK,CAAA,mBAAA,uCAAiC,CAAC,CAAC;yBACpD,EAAE,OAAO,CAAC,CAAC;AACZ,wBAAA,OAAO,GAAG;AACR,4BAAA,cAAc,EAAA,cAAA;4BACd,SAAS,EAAT,UAAU,KAAY,EAAA;gCACpB,IAAM,YAAY,GAAG,KAA8C,CAAC;AACpE,gCAAA,IAAI,YAAY,CAAC,IAAI,CAAC,OAAO,KAAK,OAAO,EAAE;oCACzC,OAAO;AACR,iCAAA;AACD,gCAAA,QAAQ,YAAY,CAAC,IAAI,CAAC,MAAM;AAC9B,oCAAA,KAAA,KAAA;;wCAEE,YAAY,CAAC,QAAQ,CAAC,CAAC;wCACvB,eAAe,GAAG,UAAU,CAAC,YAAA;AAC3B,4CAAA,MAAM,CAAC,IAAI,KAAK,CAAA,SAAA,6BAAuB,CAAC,CAAC;AAC3C,yCAAC,yCAA8B,CAAC;wCAChC,MAAM;AACR,oCAAA,KAAA,MAAA;;wCAEE,YAAY,CAAC,eAAe,CAAC,CAAC;AAC9B,wCAAA,OAAO,CAAC,YAAY,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;wCACpC,MAAM;AACR,oCAAA;wCACE,YAAY,CAAC,QAAQ,CAAC,CAAC;wCACvB,YAAY,CAAC,eAAe,CAAC,CAAC;AAC9B,wCAAA,MAAM,CAAC,IAAI,KAAK,CAAA,kBAAA,sCAAgC,CAAC,CAAC;wCAClD,MAAM;AACT,iCAAA;6BACF;yBACF,CAAC;AACF,wBAAA,KAAI,CAAC,QAAQ,CAAC,GAAG,CAAC,OAAO,CAAC,CAAC;wBAC3B,cAAc,CAAC,KAAK,CAAC,gBAAgB,CAAC,SAAS,EAAE,OAAO,CAAC,SAAS,CAAC,CAAC;AACpE,wBAAA,KAAI,CAAC,MAAM,CAAC,WAAW,CACrB;AACE,4BAAA,SAAS,EAAA,SAAA;AACT,4BAAA,OAAO,EAAA,OAAA;AACP,4BAAA,IAAI,EAAA,IAAA;AACoB,yBAAA,EAC1B,CAAC,cAAc,CAAC,KAAK,CAAC,CACvB,CAAC;qBACH,CAAC,CAAC,OAAO,CAAC,YAAA;AACT,wBAAA,IAAI,OAAO,EAAE;AACX,4BAAA,KAAI,CAAC,oBAAoB,CAAC,OAAO,CAAC,CAAC;AACpC,yBAAA;AACH,qBAAC,CAAC,CAAC,CAAA;;;AACJ,KAAA,CAAA;IACH,OAAC,MAAA,CAAA;AAAD,CAAC,EAAA,CAAA;;AC3ID;;;;;;;;;;;;;;;AAeG;AA4BI,IAAM,OAAO,GAAG,wBAAwB,CAAC;AAChD,IAAM,UAAU,GAAG,CAAC,CAAC;AACrB,IAAM,mBAAmB,GAAG,sBAAsB,CAAC;AACnD,IAAM,eAAe,GAAG,WAAW,CAAC;AAOpC;;;;;AAKG;AACH,IAAA,SAAA,kBAAA,YAAA;AACE,IAAA,SAAA,SAAA,CAA6B,OAAmB,EAAA;QAAnB,IAAO,CAAA,OAAA,GAAP,OAAO,CAAY;KAAI;AAEpD,IAAA,SAAA,CAAA,SAAA,CAAA,SAAS,GAAT,YAAA;QAAA,IASC,KAAA,GAAA,IAAA,CAAA;AARC,QAAA,OAAO,IAAI,OAAO,CAAI,UAAC,OAAO,EAAE,MAAM,EAAA;AACpC,YAAA,KAAI,CAAC,OAAO,CAAC,gBAAgB,CAAC,SAAS,EAAE,YAAA;AACvC,gBAAA,OAAO,CAAC,KAAI,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC;AAC/B,aAAC,CAAC,CAAC;AACH,YAAA,KAAI,CAAC,OAAO,CAAC,gBAAgB,CAAC,OAAO,EAAE,YAAA;AACrC,gBAAA,MAAM,CAAC,KAAI,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC;AAC7B,aAAC,CAAC,CAAC;AACL,SAAC,CAAC,CAAC;KACJ,CAAA;IACH,OAAC,SAAA,CAAA;AAAD,CAAC,EAAA,CAAA,CAAA;AAED,SAAS,cAAc,CAAC,EAAe,EAAE,WAAoB,EAAA;AAC3D,IAAA,OAAO,EAAE;AACN,SAAA,WAAW,CAAC,CAAC,mBAAmB,CAAC,EAAE,WAAW,GAAG,WAAW,GAAG,UAAU,CAAC;SAC1E,WAAW,CAAC,mBAAmB,CAAC,CAAC;AACtC,CAAC;SAOe,eAAe,GAAA;IAC7B,IAAM,OAAO,GAAG,SAAS,CAAC,cAAc,CAAC,OAAO,CAAC,CAAC;IAClD,OAAO,IAAI,SAAS,CAAO,OAAO,CAAC,CAAC,SAAS,EAAE,CAAC;AAClD,CAAC;SAEe,aAAa,GAAA;IAA7B,IAkCC,KAAA,GAAA,IAAA,CAAA;IAjCC,IAAM,OAAO,GAAG,SAAS,CAAC,IAAI,CAAC,OAAO,EAAE,UAAU,CAAC,CAAC;AACpD,IAAA,OAAO,IAAI,OAAO,CAAC,UAAC,OAAO,EAAE,MAAM,EAAA;AACjC,QAAA,OAAO,CAAC,gBAAgB,CAAC,OAAO,EAAE,YAAA;AAChC,YAAA,MAAM,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC;AACxB,SAAC,CAAC,CAAC;AAEH,QAAA,OAAO,CAAC,gBAAgB,CAAC,eAAe,EAAE,YAAA;AACxC,YAAA,IAAM,EAAE,GAAG,OAAO,CAAC,MAAM,CAAC;YAE1B,IAAI;gBACF,EAAE,CAAC,iBAAiB,CAAC,mBAAmB,EAAE,EAAE,OAAO,EAAE,eAAe,EAAE,CAAC,CAAC;AACzE,aAAA;AAAC,YAAA,OAAO,CAAC,EAAE;gBACV,MAAM,CAAC,CAAC,CAAC,CAAC;AACX,aAAA;AACH,SAAC,CAAC,CAAC;AAEH,QAAA,OAAO,CAAC,gBAAgB,CAAC,SAAS,EAAE,YAAA,EAAA,OAAAD,eAAA,CAAA,KAAA,EAAA,KAAA,CAAA,EAAA,KAAA,CAAA,EAAA,YAAA;;;;;AAC5B,wBAAA,EAAE,GAAgB,OAAO,CAAC,MAAM,CAAC;6BAMnC,CAAC,EAAE,CAAC,gBAAgB,CAAC,QAAQ,CAAC,mBAAmB,CAAC,EAAlD,OAAkD,CAAA,CAAA,YAAA,CAAA,CAAA,CAAA;;wBAEpD,EAAE,CAAC,KAAK,EAAE,CAAC;wBACX,OAAM,CAAA,CAAA,YAAA,eAAe,EAAE,CAAA,CAAA;;AAAvB,wBAAA,EAAA,CAAA,IAAA,EAAuB,CAAC;AACxB,wBAAA,EAAA,GAAA,OAAO,CAAA;wBAAC,OAAM,CAAA,CAAA,YAAA,aAAa,EAAE,CAAA,CAAA;;wBAA7B,EAAQ,CAAA,KAAA,CAAA,KAAA,CAAA,EAAA,CAAA,EAAA,CAAA,IAAA,EAAqB,EAAC,CAAC;;;wBAE/B,OAAO,CAAC,EAAE,CAAC,CAAC;;;;;AAEf,SAAA,CAAA,CAAA,EAAA,CAAC,CAAC;AACL,KAAC,CAAC,CAAC;AACL,CAAC;SAEqB,UAAU,CAC9B,EAAe,EACf,GAAW,EACX,KAAgC,EAAA;;;;;YAE1B,OAAO,GAAG,cAAc,CAAC,EAAE,EAAE,IAAI,CAAC,CAAC,GAAG,EAAA,EAAA,GAAA,EAAA;gBAC1C,EAAC,CAAA,eAAe,IAAG,GAAG;AACtB,gBAAA,EAAA,CAAA,KAAK,GAAA,KAAA;oBACL,CAAC;YACH,OAAO,CAAA,CAAA,aAAA,IAAI,SAAS,CAAO,OAAO,CAAC,CAAC,SAAS,EAAE,CAAC,CAAA;;;AACjD,CAAA;AAED,SAAe,SAAS,CACtB,EAAe,EACf,GAAW,EAAA;;;;;;AAEL,oBAAA,OAAO,GAAG,cAAc,CAAC,EAAE,EAAE,KAAK,CAAC,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;oBACtC,OAAM,CAAA,CAAA,YAAA,IAAI,SAAS,CAAuB,OAAO,CAAC,CAAC,SAAS,EAAE,CAAA,CAAA;;AAArE,oBAAA,IAAI,GAAG,EAA8D,CAAA,IAAA,EAAA,CAAA;AAC3E,oBAAA,OAAA,CAAA,CAAA,aAAO,IAAI,KAAK,SAAS,GAAG,IAAI,GAAG,IAAI,CAAC,KAAK,CAAC,CAAA;;;;AAC/C,CAAA;AAEe,SAAA,aAAa,CAAC,EAAe,EAAE,GAAW,EAAA;AACxD,IAAA,IAAM,OAAO,GAAG,cAAc,CAAC,EAAE,EAAE,IAAI,CAAC,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC;IACrD,OAAO,IAAI,SAAS,CAAO,OAAO,CAAC,CAAC,SAAS,EAAE,CAAC;AAClD,CAAC;AAEM,IAAM,oBAAoB,GAAG,GAAG,CAAC;AACjC,IAAM,wBAAwB,GAAG,CAAC,CAAC;AAE1C,IAAA,yBAAA,kBAAA,YAAA;AAqBE,IAAA,SAAA,yBAAA,GAAA;AAlBA,QAAA,IAAA,CAAA,IAAI,GAAyB,OAAA,6BAAA;QAEpB,IAAqB,CAAA,qBAAA,GAAG,IAAI,CAAC;QAErB,IAAS,CAAA,SAAA,GAA8C,EAAE,CAAC;QAC1D,IAAU,CAAA,UAAA,GAA4C,EAAE,CAAC;;;QAGlE,IAAS,CAAA,SAAA,GAAe,IAAI,CAAC;QAC7B,IAAa,CAAA,aAAA,GAAG,CAAC,CAAC;QAElB,IAAQ,CAAA,QAAA,GAAoB,IAAI,CAAC;QACjC,IAAM,CAAA,MAAA,GAAkB,IAAI,CAAC;QAC7B,IAA8B,CAAA,8BAAA,GAAG,KAAK,CAAC;QACvC,IAAmB,CAAA,mBAAA,GAAyB,IAAI,CAAC;;AAMvD,QAAA,IAAI,CAAC,4BAA4B;AAC/B,YAAA,IAAI,CAAC,gCAAgC,EAAE,CAAC,IAAI,CAC1C,YAAO,GAAC,EACR,YAAA,GAAQ,CACT,CAAC;KACL;AAEK,IAAA,yBAAA,CAAA,SAAA,CAAA,OAAO,GAAb,YAAA;;;;;;wBACE,IAAI,IAAI,CAAC,EAAE,EAAE;4BACX,OAAO,CAAA,CAAA,aAAA,IAAI,CAAC,EAAE,CAAC,CAAA;AAChB,yBAAA;AACD,wBAAA,EAAA,GAAA,IAAI,CAAA;wBAAM,OAAM,CAAA,CAAA,YAAA,aAAa,EAAE,CAAA,CAAA;;wBAA/B,EAAK,CAAA,EAAE,GAAG,EAAA,CAAA,IAAA,EAAqB,CAAC;wBAChC,OAAO,CAAA,CAAA,aAAA,IAAI,CAAC,EAAE,CAAC,CAAA;;;;AAChB,KAAA,CAAA;IAEK,yBAAY,CAAA,SAAA,CAAA,YAAA,GAAlB,UAAsB,EAAmC,EAAA;;;;;;wBACnD,WAAW,GAAG,CAAC,CAAC;;;;;;AAIL,wBAAA,OAAA,CAAA,CAAA,YAAM,IAAI,CAAC,OAAO,EAAE,CAAA,CAAA;;AAAzB,wBAAA,EAAE,GAAG,EAAoB,CAAA,IAAA,EAAA,CAAA;AACxB,wBAAA,OAAA,CAAA,CAAA,YAAM,EAAE,CAAC,EAAE,CAAC,CAAA,CAAA;AAAnB,oBAAA,KAAA,CAAA,EAAA,OAAA,CAAA,CAAA,aAAO,SAAY,CAAC,CAAA;;;AAEpB,wBAAA,IAAI,WAAW,EAAE,GAAG,wBAAwB,EAAE;AAC5C,4BAAA,MAAM,GAAC,CAAC;AACT,yBAAA;wBACD,IAAI,IAAI,CAAC,EAAE,EAAE;AACX,4BAAA,IAAI,CAAC,EAAE,CAAC,KAAK,EAAE,CAAC;AAChB,4BAAA,IAAI,CAAC,EAAE,GAAG,SAAS,CAAC;AACrB,yBAAA;;;;;;;AAIN,KAAA,CAAA;AAED;;;AAGG;AACW,IAAA,yBAAA,CAAA,SAAA,CAAA,gCAAgC,GAA9C,YAAA;;;AACE,gBAAA,OAAA,CAAA,CAAA,aAAOE,eAAS,EAAE,GAAG,IAAI,CAAC,kBAAkB,EAAE,GAAG,IAAI,CAAC,gBAAgB,EAAE,CAAC,CAAA;;;AAC1E,KAAA,CAAA;AAED;;AAEG;AACW,IAAA,yBAAA,CAAA,SAAA,CAAA,kBAAkB,GAAhC,YAAA;;;;gBACE,IAAI,CAAC,QAAQ,GAAG,QAAQ,CAAC,YAAY,CAACC,2BAAqB,EAAG,CAAC,CAAC;;gBAEhE,IAAI,CAAC,QAAQ,CAAC,UAAU,4CAEtB,UAAO,OAAe,EAAE,IAAuB,EAAA,EAAA,OAAAH,eAAA,CAAA,KAAA,EAAA,KAAA,CAAA,EAAA,KAAA,CAAA,EAAA,YAAA;;;;AAChC,4BAAA,KAAA,CAAA,EAAA,OAAA,CAAA,CAAA,YAAM,IAAI,CAAC,KAAK,EAAE,CAAA,CAAA;;AAAzB,gCAAA,IAAI,GAAG,EAAkB,CAAA,IAAA,EAAA,CAAA;gCAC/B,OAAO,CAAA,CAAA,aAAA;wCACL,YAAY,EAAE,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,GAAG,CAAC;qCACtC,CAAC,CAAA;;;AACH,iBAAA,CAAA,CAAA,EAAA,CACF,CAAC;;gBAEF,IAAI,CAAC,QAAQ,CAAC,UAAU,+BAEtB,UAAO,OAAe,EAAE,KAAkB,EAAA,EAAA,OAAAA,eAAA,CAAA,KAAA,EAAA,KAAA,CAAA,EAAA,KAAA,CAAA,EAAA,YAAA;;AACxC,wBAAA,OAAA,CAAA,CAAA,aAAO,2CAAwB,CAAC,CAAA;;AACjC,iBAAA,CAAA,CAAA,EAAA,CACF,CAAC;;;;AACH,KAAA,CAAA;AAED;;;;;;AAMG;AACW,IAAA,yBAAA,CAAA,SAAA,CAAA,gBAAgB,GAA9B,YAAA;;;;;;;;AAEE,wBAAA,EAAA,GAAA,IAAI,CAAA;wBAAuB,OAAM,CAAA,CAAA,YAAAI,6BAAuB,EAAE,CAAA,CAAA;;;wBAA1D,EAAK,CAAA,mBAAmB,GAAG,EAAA,CAAA,IAAA,EAA+B,CAAC;AAC3D,wBAAA,IAAI,CAAC,IAAI,CAAC,mBAAmB,EAAE;4BAC7B,OAAO,CAAA,CAAA,YAAA,CAAA;AACR,yBAAA;wBACD,IAAI,CAAC,MAAM,GAAG,IAAI,MAAM,CAAC,IAAI,CAAC,mBAAmB,CAAC,CAAC;wBAEnC,OAAM,CAAA,CAAA,YAAA,IAAI,CAAC,MAAM,CAAC,KAAK,CAErC,MAAA,wBAAA,EAAE,sCAEH,CAAA,CAAA;;AAJK,wBAAA,OAAO,GAAG,EAIf,CAAA,IAAA,EAAA,CAAA;wBACD,IAAI,CAAC,OAAO,EAAE;4BACZ,OAAO,CAAA,CAAA,YAAA,CAAA;AACR,yBAAA;AACD,wBAAA,IACE,CAAA,CAAA,EAAA,GAAA,OAAO,CAAC,CAAC,CAAC,0CAAE,SAAS;6BACrB,CAAA,EAAA,GAAA,OAAO,CAAC,CAAC,CAAC,MAAA,IAAA,IAAA,EAAA,KAAA,KAAA,CAAA,GAAA,KAAA,CAAA,GAAA,EAAA,CAAE,KAAK,CAAC,QAAQ,CAAwB,YAAA,8BAAA,CAAA,EAClD;AACA,4BAAA,IAAI,CAAC,8BAA8B,GAAG,IAAI,CAAC;AAC5C,yBAAA;;;;;AACF,KAAA,CAAA;AAED;;;;;;;;AAQG;IACW,yBAAmB,CAAA,SAAA,CAAA,mBAAA,GAAjC,UAAkC,GAAW,EAAA;;;;;wBAC3C,IACE,CAAC,IAAI,CAAC,MAAM;4BACZ,CAAC,IAAI,CAAC,mBAAmB;AACzB,4BAAAC,iCAA2B,EAAE,KAAK,IAAI,CAAC,mBAAmB,EAC1D;4BACA,OAAO,CAAA,CAAA,YAAA,CAAA;AACR,yBAAA;;;;wBAEC,OAAM,CAAA,CAAA,YAAA,IAAI,CAAC,MAAM,CAAC,KAAK,CAErB,YAAA,+BAAA,EAAE,GAAG,EAAA,GAAA,EAAE;;AAEP,4BAAA,IAAI,CAAC,8BAA8B;kCAChC,GAAA;kCACA,EAAA,4BACJ,CAAA,CAAA;;AAPD,wBAAA,EAAA,CAAA,IAAA,EAOC,CAAC;;;;;;;;;AAIL,KAAA,CAAA;AAEK,IAAA,yBAAA,CAAA,SAAA,CAAA,YAAY,GAAlB,YAAA;;;;;;;wBAEI,IAAI,CAAC,SAAS,EAAE;AACd,4BAAA,OAAA,CAAA,CAAA,aAAO,KAAK,CAAC,CAAA;AACd,yBAAA;wBACU,OAAM,CAAA,CAAA,YAAA,aAAa,EAAE,CAAA,CAAA;;AAA1B,wBAAA,EAAE,GAAG,EAAqB,CAAA,IAAA,EAAA,CAAA;wBAChC,OAAM,CAAA,CAAA,YAAA,UAAU,CAAC,EAAE,EAAEV,2BAAqB,EAAE,GAAG,CAAC,CAAA,CAAA;;AAAhD,wBAAA,EAAA,CAAA,IAAA,EAAgD,CAAC;AACjD,wBAAA,OAAA,CAAA,CAAA,YAAM,aAAa,CAAC,EAAE,EAAEA,2BAAqB,CAAC,CAAA,CAAA;;AAA9C,wBAAA,EAAA,CAAA,IAAA,EAA8C,CAAC;AAC/C,wBAAA,OAAA,CAAA,CAAA,aAAO,IAAI,CAAC,CAAA;;;;AAEd,oBAAA,KAAA,CAAA,EAAA,OAAA,CAAA,CAAA,aAAO,KAAK,CAAC,CAAA;;;;AACd,KAAA,CAAA;IAEa,yBAAiB,CAAA,SAAA,CAAA,iBAAA,GAA/B,UAAgC,KAA0B,EAAA;;;;;wBACxD,IAAI,CAAC,aAAa,EAAE,CAAC;;;;wBAEnB,OAAM,CAAA,CAAA,YAAA,KAAK,EAAE,CAAA,CAAA;;AAAb,wBAAA,EAAA,CAAA,IAAA,EAAa,CAAC;;;wBAEd,IAAI,CAAC,aAAa,EAAE,CAAC;;;;;;AAExB,KAAA,CAAA;AAEK,IAAA,yBAAA,CAAA,SAAA,CAAA,IAAI,GAAV,UAAW,GAAW,EAAE,KAAuB,EAAA;;;;gBAC7C,OAAO,CAAA,CAAA,aAAA,IAAI,CAAC,iBAAiB,CAAC,YAAA,EAAA,OAAAK,eAAA,CAAA,KAAA,EAAA,KAAA,CAAA,EAAA,KAAA,CAAA,EAAA,YAAA;;;AAC5B,gCAAA,KAAA,CAAA,EAAA,OAAA,CAAA,CAAA,YAAM,IAAI,CAAC,YAAY,CAAC,UAAC,EAAe,IAAK,OAAA,UAAU,CAAC,EAAE,EAAE,GAAG,EAAE,KAAK,CAAC,CAA1B,EAA0B,CAAC,CAAA,CAAA;;AAAxE,oCAAA,EAAA,CAAA,IAAA,EAAwE,CAAC;AACzE,oCAAA,IAAI,CAAC,UAAU,CAAC,GAAG,CAAC,GAAG,KAAK,CAAC;AAC7B,oCAAA,OAAA,CAAA,CAAA,aAAO,IAAI,CAAC,mBAAmB,CAAC,GAAG,CAAC,CAAC,CAAA;;;AACtC,qBAAA,CAAA,CAAA,EAAA,CAAC,CAAC,CAAA;;;AACJ,KAAA,CAAA;IAEK,yBAAI,CAAA,SAAA,CAAA,IAAA,GAAV,UAAuC,GAAW,EAAA;;;;;AACnC,oBAAA,KAAA,CAAA,EAAA,OAAA,CAAA,CAAA,YAAM,IAAI,CAAC,YAAY,CAAC,UAAC,EAAe,EAAA;AACnD,4BAAA,OAAA,SAAS,CAAC,EAAE,EAAE,GAAG,CAAC,CAAA;AAAlB,yBAAkB,CACnB,CAAA,CAAA;;wBAFK,GAAG,IAAI,EAAA,CAAA,IAAA,EAEZ,CAAM,CAAA;AACP,wBAAA,IAAI,CAAC,UAAU,CAAC,GAAG,CAAC,GAAG,GAAG,CAAC;AAC3B,wBAAA,OAAA,CAAA,CAAA,aAAO,GAAG,CAAC,CAAA;;;;AACZ,KAAA,CAAA;IAEK,yBAAO,CAAA,SAAA,CAAA,OAAA,GAAb,UAAc,GAAW,EAAA;;;;gBACvB,OAAO,CAAA,CAAA,aAAA,IAAI,CAAC,iBAAiB,CAAC,YAAA,EAAA,OAAAA,eAAA,CAAA,KAAA,EAAA,KAAA,CAAA,EAAA,KAAA,CAAA,EAAA,YAAA;;;AAC5B,gCAAA,KAAA,CAAA,EAAA,OAAA,CAAA,CAAA,YAAM,IAAI,CAAC,YAAY,CAAC,UAAC,EAAe,EAAK,EAAA,OAAA,aAAa,CAAC,EAAE,EAAE,GAAG,CAAC,CAAtB,EAAsB,CAAC,CAAA,CAAA;;AAApE,oCAAA,EAAA,CAAA,IAAA,EAAoE,CAAC;AACrE,oCAAA,OAAO,IAAI,CAAC,UAAU,CAAC,GAAG,CAAC,CAAC;AAC5B,oCAAA,OAAA,CAAA,CAAA,aAAO,IAAI,CAAC,mBAAmB,CAAC,GAAG,CAAC,CAAC,CAAA;;;AACtC,qBAAA,CAAA,CAAA,EAAA,CAAC,CAAC,CAAA;;;AACJ,KAAA,CAAA;AAEa,IAAA,yBAAA,CAAA,SAAA,CAAA,KAAK,GAAnB,YAAA;;;;;AAEiB,oBAAA,KAAA,CAAA,EAAA,OAAA,CAAA,CAAA,YAAM,IAAI,CAAC,YAAY,CAAC,UAAC,EAAe,EAAA;4BACrD,IAAM,aAAa,GAAG,cAAc,CAAC,EAAE,EAAE,KAAK,CAAC,CAAC,MAAM,EAAE,CAAC;4BACzD,OAAO,IAAI,SAAS,CAAoB,aAAa,CAAC,CAAC,SAAS,EAAE,CAAC;AACrE,yBAAC,CAAC,CAAA,CAAA;;AAHI,wBAAA,MAAM,GAAG,EAGb,CAAA,IAAA,EAAA,CAAA;wBAEF,IAAI,CAAC,MAAM,EAAE;AACX,4BAAA,OAAA,CAAA,CAAA,aAAO,EAAE,CAAC,CAAA;AACX,yBAAA;;AAGD,wBAAA,IAAI,IAAI,CAAC,aAAa,KAAK,CAAC,EAAE;AAC5B,4BAAA,OAAA,CAAA,CAAA,aAAO,EAAE,CAAC,CAAA;AACX,yBAAA;wBAEK,IAAI,GAAG,EAAE,CAAC;AACV,wBAAA,YAAY,GAAG,IAAI,GAAG,EAAE,CAAC;AAC/B,wBAAA,IAAI,MAAM,CAAC,MAAM,KAAK,CAAC,EAAE;AACvB,4BAAA,KAAA,EAAA,GAAA,CAA8C,EAAN,QAAM,GAAA,MAAA,EAAN,EAAM,GAAA,QAAA,CAAA,MAAA,EAAN,IAAM,EAAE;AAArC,gCAAA,EAAA,GAAA,QAAA,CAAA,EAAA,CAAyB,EAAZ,GAAG,GAAA,EAAA,CAAA,SAAA,EAAE,KAAK,GAAA,EAAA,CAAA,KAAA,CAAA;AAChC,gCAAA,YAAY,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;AACtB,gCAAA,IAAI,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,UAAU,CAAC,GAAG,CAAC,CAAC,KAAK,IAAI,CAAC,SAAS,CAAC,KAAK,CAAC,EAAE;AAClE,oCAAA,IAAI,CAAC,eAAe,CAAC,GAAG,EAAE,KAAyB,CAAC,CAAC;AACrD,oCAAA,IAAI,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;AAChB,iCAAA;AACF,6BAAA;AACF,yBAAA;AAED,wBAAA,KAAA,EAAA,GAAA,CAAmD,EAA5B,EAAA,GAAA,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,UAAU,CAAC,EAA5B,EAA4B,GAAA,EAAA,CAAA,MAAA,EAA5B,IAA4B,EAAE;4BAA1C,QAAQ,GAAA,EAAA,CAAA,EAAA,CAAA,CAAA;AACjB,4BAAA,IAAI,IAAI,CAAC,UAAU,CAAC,QAAQ,CAAC,IAAI,CAAC,YAAY,CAAC,GAAG,CAAC,QAAQ,CAAC,EAAE;;AAE5D,gCAAA,IAAI,CAAC,eAAe,CAAC,QAAQ,EAAE,IAAI,CAAC,CAAC;AACrC,gCAAA,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;AACrB,6BAAA;AACF,yBAAA;AACD,wBAAA,OAAA,CAAA,CAAA,aAAO,IAAI,CAAC,CAAA;;;;AACb,KAAA,CAAA;AAEO,IAAA,yBAAA,CAAA,SAAA,CAAA,eAAe,GAAvB,UACE,GAAW,EACX,QAAiC,EAAA;AAEjC,QAAA,IAAI,CAAC,UAAU,CAAC,GAAG,CAAC,GAAG,QAAQ,CAAC;QAChC,IAAM,SAAS,GAAG,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC,CAAC;AACtC,QAAA,IAAI,SAAS,EAAE;AACb,YAAA,KAAuB,IAAqB,EAAA,GAAA,CAAA,EAArB,EAAA,GAAA,KAAK,CAAC,IAAI,CAAC,SAAS,CAAC,EAArB,EAAqB,GAAA,EAAA,CAAA,MAAA,EAArB,IAAqB,EAAE;AAAzC,gBAAA,IAAM,QAAQ,GAAA,EAAA,CAAA,EAAA,CAAA,CAAA;gBACjB,QAAQ,CAAC,QAAQ,CAAC,CAAC;AACpB,aAAA;AACF,SAAA;KACF,CAAA;AAEO,IAAA,yBAAA,CAAA,SAAA,CAAA,YAAY,GAApB,YAAA;QAAA,IAOC,KAAA,GAAA,IAAA,CAAA;QANC,IAAI,CAAC,WAAW,EAAE,CAAC;AAEnB,QAAA,IAAI,CAAC,SAAS,GAAG,WAAW,CAC1B,YAAA,EAAA,OAAAA,eAAA,CAAA,KAAA,EAAA,KAAA,CAAA,EAAA,KAAA,CAAA,EAAA,YAAA,EAAA,OAAAC,iBAAA,CAAA,IAAA,EAAA,UAAA,EAAA,EAAA;AAAY,YAAA,OAAA,CAAA,CAAA,aAAA,IAAI,CAAC,KAAK,EAAE,CAAA,CAAA;iBAAA,EACxB,oBAAoB,CACrB,CAAC;KACH,CAAA;AAEO,IAAA,yBAAA,CAAA,SAAA,CAAA,WAAW,GAAnB,YAAA;QACE,IAAI,IAAI,CAAC,SAAS,EAAE;AAClB,YAAA,aAAa,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;AAC9B,YAAA,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC;AACvB,SAAA;KACF,CAAA;AAED,IAAA,yBAAA,CAAA,SAAA,CAAA,YAAY,GAAZ,UAAa,GAAW,EAAE,QAA8B,EAAA;AACtD,QAAA,IAAI,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC,MAAM,KAAK,CAAC,EAAE;YAC5C,IAAI,CAAC,YAAY,EAAE,CAAC;AACrB,SAAA;AACD,QAAA,IAAI,CAAC,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC,EAAE;YACxB,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC,GAAG,IAAI,GAAG,EAAE,CAAC;;YAEhC,KAAK,IAAI,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;AACrB,SAAA;QACD,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC,QAAQ,CAAC,CAAC;KACnC,CAAA;AAED,IAAA,yBAAA,CAAA,SAAA,CAAA,eAAe,GAAf,UAAgB,GAAW,EAAE,QAA8B,EAAA;AACzD,QAAA,IAAI,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC,EAAE;YACvB,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC,CAAC,MAAM,CAAC,QAAQ,CAAC,CAAC;YAErC,IAAI,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC,CAAC,IAAI,KAAK,CAAC,EAAE;AAClC,gBAAA,OAAO,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC,CAAC;AAC5B,aAAA;AACF,SAAA;AAED,QAAA,IAAI,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC,MAAM,KAAK,CAAC,EAAE;YAC5C,IAAI,CAAC,WAAW,EAAE,CAAC;AACpB,SAAA;KACF,CAAA;IAhSM,yBAAI,CAAA,IAAA,GAAY,OAAO,CAAC;IAiSjC,OAAC,yBAAA,CAAA;AAAA,CAlSD,EAkSC,CAAA,CAAA;AAED;;;;;AAKG;AACI,IAAM,yBAAyB,GAAgB;;ACrctD;;;;;;;;;;;;;;;AAeG;AASH;;;;AAIG;AACa,SAAA,oBAAoB,CAClC,IAAkB,EAClB,gBAAmD,EAAA;AAEnD,IAAA,IAAI,gBAAgB,EAAE;AACpB,QAAA,OAAOK,kBAAY,CAAC,gBAAgB,CAAC,CAAC;AACvC,KAAA;AAED,IAAAC,aAAO,CAAC,IAAI,CAAC,sBAAsB,EAAE,IAAI,sDAA+B,CAAC;IAEzE,OAAO,IAAI,CAAC,sBAAsB,CAAC;AACrC;;ACxCA;;;;;;;;;;;;;;;AAeG;AAiCH,IAAA,aAAA,kBAAA,UAAA,MAAA,EAAA;IAA4BV,eAAc,CAAA,aAAA,EAAA,MAAA,CAAA,CAAA;AACxC,IAAA,SAAA,aAAA,CAAqB,MAAqB,EAAA;AAA1C,QAAA,IAAA,KAAA,GACE,qFAA2C,IAC5C,IAAA,CAAA;QAFoB,KAAM,CAAA,MAAA,GAAN,MAAM,CAAe;;KAEzC;IAED,aAAmB,CAAA,SAAA,CAAA,mBAAA,GAAnB,UAAoB,IAAkB,EAAA;QACpC,OAAOW,mBAAa,CAAC,IAAI,EAAE,IAAI,CAAC,gBAAgB,EAAE,CAAC,CAAC;KACrD,CAAA;AAED,IAAA,aAAA,CAAA,SAAA,CAAA,cAAc,GAAd,UACE,IAAkB,EAClB,OAAe,EAAA;QAEf,OAAOA,mBAAa,CAAC,IAAI,EAAE,IAAI,CAAC,gBAAgB,CAAC,OAAO,CAAC,CAAC,CAAC;KAC5D,CAAA;IAED,aAA4B,CAAA,SAAA,CAAA,4BAAA,GAA5B,UAA6B,IAAkB,EAAA;QAC7C,OAAOA,mBAAa,CAAC,IAAI,EAAE,IAAI,CAAC,gBAAgB,EAAE,CAAC,CAAC;KACrD,CAAA;IAEO,aAAgB,CAAA,SAAA,CAAA,gBAAA,GAAxB,UAAyB,OAAgB,EAAA;AACvC,QAAA,IAAM,OAAO,GAAyB;AACpC,YAAA,UAAU,EAAE,IAAI,CAAC,MAAM,CAAC,UAAU;AAClC,YAAA,SAAS,EAAE,IAAI,CAAC,MAAM,CAAC,SAAS;AAChC,YAAA,QAAQ,EAAE,IAAI,CAAC,MAAM,CAAC,QAAQ;AAC9B,YAAA,QAAQ,EAAE,IAAI,CAAC,MAAM,CAAC,QAAQ;AAC9B,YAAA,YAAY,EAAE,IAAI,CAAC,MAAM,CAAC,YAAY;AACtC,YAAA,iBAAiB,EAAE,IAAI;AACvB,YAAA,mBAAmB,EAAE,IAAI;SAC1B,CAAC;AAEF,QAAA,IAAI,OAAO,EAAE;AACX,YAAA,OAAO,CAAC,OAAO,GAAG,OAAO,CAAC;AAC3B,SAAA;AAED,QAAA,OAAO,OAAO,CAAC;KAChB,CAAA;IACH,OAAC,aAAA,CAAA;AAAD,CArCA,CAA4BC,oBAAc,CAqCzC,CAAA,CAAA;AAEK,SAAU,OAAO,CACrB,MAAqB,EAAA;AAErB,IAAA,OAAOC,2BAAqB,CAC1B,MAAM,CAAC,IAAI,EACX,IAAI,aAAa,CAAC,MAAM,CAAC,EACzB,MAAM,CAAC,eAAe,CACY,CAAC;AACvC,CAAC;AAEK,SAAU,OAAO,CACrB,MAAqB,EAAA;IAEb,IAAA,IAAI,GAAW,MAAM,CAAA,IAAjB,EAAE,IAAI,GAAK,MAAM,CAAA,IAAX,CAAY;AAC9B,IAAAH,aAAO,CAAC,IAAI,EAAE,IAAI,sDAA+B,CAAC;AAClD,IAAA,OAAOI,qBAAe,CACpB,IAAI,EACJ,IAAI,aAAa,CAAC,MAAM,CAAC,EACzB,MAAM,CAAC,eAAe,CACvB,CAAC;AACJ,CAAC;AAEK,SAAgB,KAAK,CACzB,MAAqB,EAAA;;;;YAEb,IAAI,GAAW,MAAM,CAAjB,IAAA,EAAE,IAAI,GAAK,MAAM,KAAX,CAAY;AAC9B,YAAAJ,aAAO,CAAC,IAAI,EAAE,IAAI,sDAA+B,CAAC;AAClD,YAAA,OAAA,CAAA,CAAA,aAAOK,WAAS,CAAC,IAAI,EAAE,IAAI,aAAa,CAAC,MAAM,CAAC,EAAE,MAAM,CAAC,eAAe,CAAC,CAAC,CAAA;;;AAC3E;;ACnHD;;;;;;;;;;;;;;;AAeG;AA4BH;;;AAGG;AACH,IAAA,8BAAA,kBAAA,YAAA;IASE,SACqB,8BAAA,CAAA,IAAkB,EACrC,MAAuC,EACpB,QAAuC,EAChD,IAAmB,EACV,eAAuB,EAAA;AAAvB,QAAA,IAAA,eAAA,KAAA,KAAA,CAAA,EAAA,EAAA,eAAuB,GAAA,KAAA,CAAA,EAAA;QAJvB,IAAI,CAAA,IAAA,GAAJ,IAAI,CAAc;QAElB,IAAQ,CAAA,QAAA,GAAR,QAAQ,CAA+B;QAChD,IAAI,CAAA,IAAA,GAAJ,IAAI,CAAe;QACV,IAAe,CAAA,eAAA,GAAf,eAAe,CAAQ;QAXpC,IAAc,CAAA,cAAA,GAA0B,IAAI,CAAC;QAC7C,IAAY,CAAA,YAAA,GAAwB,IAAI,CAAC;AAY/C,QAAA,IAAI,CAAC,MAAM,GAAG,KAAK,CAAC,OAAO,CAAC,MAAM,CAAC,GAAG,MAAM,GAAG,CAAC,MAAM,CAAC,CAAC;KACzD;AAID,IAAA,8BAAA,CAAA,SAAA,CAAA,OAAO,GAAP,YAAA;QAAA,IAcC,KAAA,GAAA,IAAA,CAAA;AAbC,QAAA,OAAO,IAAI,OAAO,CAChB,UAAO,OAAO,EAAE,MAAM,EAAA,EAAA,OAAAZ,eAAA,CAAA,KAAA,EAAA,KAAA,CAAA,EAAA,KAAA,CAAA,EAAA,YAAA;;;;;wBACpB,IAAI,CAAC,cAAc,GAAG,EAAE,OAAO,SAAA,EAAE,MAAM,EAAA,MAAA,EAAE,CAAC;;;;AAGxC,wBAAA,EAAA,GAAA,IAAI,CAAA;wBAAgB,OAAM,CAAA,CAAA,YAAA,IAAI,CAAC,QAAQ,CAAC,WAAW,CAAC,IAAI,CAAC,IAAI,CAAC,CAAA,CAAA;;wBAA9D,EAAK,CAAA,YAAY,GAAG,EAAA,CAAA,IAAA,EAA0C,CAAC;AAC/D,wBAAA,OAAA,CAAA,CAAA,YAAM,IAAI,CAAC,WAAW,EAAE,CAAA,CAAA;;AAAxB,wBAAA,EAAA,CAAA,IAAA,EAAwB,CAAC;AACzB,wBAAA,IAAI,CAAC,YAAY,CAAC,gBAAgB,CAAC,IAAI,CAAC,CAAC;;;;AAEzC,wBAAA,IAAI,CAAC,MAAM,CAAC,GAAU,CAAC,CAAC;;;;;AAE3B,SAAA,CAAA,CAAA,EAAA,CACF,CAAC;KACH,CAAA;IAEK,8BAAW,CAAA,SAAA,CAAA,WAAA,GAAjB,UAAkB,KAAgB,EAAA;;;;;;AACxB,wBAAA,WAAW,GAAiD,KAAK,CAAtD,WAAA,EAAE,SAAS,GAAsC,KAAK,CAAA,SAA3C,EAAE,QAAQ,GAA4B,KAAK,CAAjC,QAAA,EAAE,QAAQ,GAAkB,KAAK,CAAA,QAAvB,EAAE,KAAK,GAAW,KAAK,CAAhB,KAAA,EAAE,IAAI,GAAK,KAAK,CAAA,IAAV,CAAW;AAC1E,wBAAA,IAAI,KAAK,EAAE;AACT,4BAAA,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC;4BACnB,OAAO,CAAA,CAAA,YAAA,CAAA;AACR,yBAAA;AAEK,wBAAA,MAAM,GAAkB;4BAC5B,IAAI,EAAE,IAAI,CAAC,IAAI;AACf,4BAAA,UAAU,EAAE,WAAY;AACxB,4BAAA,SAAS,EAAE,SAAU;4BACrB,QAAQ,EAAE,QAAQ,IAAI,SAAS;4BAC/B,QAAQ,EAAE,QAAQ,IAAI,SAAS;4BAC/B,IAAI,EAAE,IAAI,CAAC,IAAI;4BACf,eAAe,EAAE,IAAI,CAAC,eAAe;yBACtC,CAAC;;;;wBAGA,EAAA,GAAA,IAAI,CAAC,OAAO,CAAA;wBAAC,OAAM,CAAA,CAAA,YAAA,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,CAAC,MAAM,CAAC,CAAA,CAAA;;AAAhD,wBAAA,EAAA,CAAA,KAAA,CAAA,IAAI,EAAA,CAAS,EAAmC,CAAA,IAAA,EAAA,CAAA,CAAC,CAAC;;;;AAElD,wBAAA,IAAI,CAAC,MAAM,CAAC,GAAU,CAAC,CAAC;;;;;;AAE3B,KAAA,CAAA;IAED,8BAAO,CAAA,SAAA,CAAA,OAAA,GAAP,UAAQ,KAAoB,EAAA;AAC1B,QAAA,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC;KACpB,CAAA;IAEO,8BAAU,CAAA,SAAA,CAAA,UAAA,GAAlB,UAAmB,IAAmB,EAAA;AACpC,QAAA,QAAQ,IAAI;YACV,KAAqC,gBAAA,uCAAA;AACrC,YAAA,KAAA,mBAAA;AACE,gBAAA,OAAO,OAAO,CAAC;YACjB,KAAkC,cAAA,oCAAA;AAClC,YAAA,KAAA,iBAAA;AACE,gBAAA,OAAO,KAAK,CAAC;YACf,KAAoC,gBAAA,sCAAA;AACpC,YAAA,KAAA,mBAAA;AACE,gBAAA,OAAO,OAAO,CAAC;AACjB,YAAA;AACE,gBAAAa,WAAK,CAAC,IAAI,CAAC,IAAI,sDAA+B,CAAC;AAClD,SAAA;KACF,CAAA;IAES,8BAAO,CAAA,SAAA,CAAA,OAAA,GAAjB,UAAkB,IAAmC,EAAA;AACnD,QAAAC,iBAAW,CAAC,IAAI,CAAC,cAAc,EAAE,+BAA+B,CAAC,CAAC;AAClE,QAAA,IAAI,CAAC,cAAc,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC;QAClC,IAAI,CAAC,oBAAoB,EAAE,CAAC;KAC7B,CAAA;IAES,8BAAM,CAAA,SAAA,CAAA,MAAA,GAAhB,UAAiB,KAAY,EAAA;AAC3B,QAAAA,iBAAW,CAAC,IAAI,CAAC,cAAc,EAAE,+BAA+B,CAAC,CAAC;AAClE,QAAA,IAAI,CAAC,cAAc,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC;QAClC,IAAI,CAAC,oBAAoB,EAAE,CAAC;KAC7B,CAAA;AAEO,IAAA,8BAAA,CAAA,SAAA,CAAA,oBAAoB,GAA5B,YAAA;QACE,IAAI,IAAI,CAAC,YAAY,EAAE;AACrB,YAAA,IAAI,CAAC,YAAY,CAAC,kBAAkB,CAAC,IAAI,CAAC,CAAC;AAC5C,SAAA;AAED,QAAA,IAAI,CAAC,cAAc,GAAG,IAAI,CAAC;QAC3B,IAAI,CAAC,OAAO,EAAE,CAAC;KAChB,CAAA;IAGH,OAAC,8BAAA,CAAA;AAAD,CAAC,EAAA,CAAA;;ACtJD;;;;;;;;;;;;;;;AAeG;AA0CI,IAAM,0BAA0B,GAAG,IAAIC,WAAK,CAAC,IAAI,EAAE,KAAK,CAAC,CAAC;AAEjE;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA8BG;SACmB,eAAe,CACnC,IAAU,EACV,QAAsB,EACtB,QAAgC,EAAA;;;;AAEhC,YAAA,IAAIC,wBAAoB,CAAC,IAAI,CAAC,GAAG,CAAC,EAAE;gBAClC,OAAO,CAAA,CAAA,aAAA,OAAO,CAAC,MAAM,CACnBC,kBAAY,CAAC,IAAI,EAAwC,6CAAA,6CAAA,CAC1D,CAAC,CAAA;AACH,aAAA;AACK,YAAA,YAAY,GAAGC,eAAS,CAAC,IAAI,CAAC,CAAC;AACrC,YAAAC,uBAAiB,CAAC,IAAI,EAAE,QAAQ,EAAEC,2BAAqB,CAAC,CAAC;AACnD,YAAA,gBAAgB,GAAG,oBAAoB,CAAC,YAAY,EAAE,QAAQ,CAAC,CAAC;YAChE,MAAM,GAAG,IAAI,cAAc,CAC/B,YAAY,0DAEZ,QAAQ,EACR,gBAAgB,CACjB,CAAC;AACF,YAAA,OAAA,CAAA,CAAA,aAAO,MAAM,CAAC,cAAc,EAAE,CAAC,CAAA;;;AAChC,CAAA;AAED;;;;;;;;;;;;;;;;;;;;;;;;;;;AA2BG;SACmB,uBAAuB,CAC3C,IAAU,EACV,QAAsB,EACtB,QAAgC,EAAA;;;;AAE1B,YAAA,YAAY,GAAGC,uBAAkB,CAAC,IAAI,CAAiB,CAAC;YAC9D,IAAIL,wBAAoB,CAAC,YAAY,CAAC,IAAI,CAAC,GAAG,CAAC,EAAE;gBAC/C,OAAO,CAAA,CAAA,aAAA,OAAO,CAAC,MAAM,CACnBC,kBAAY,CAAC,YAAY,CAAC,IAAI,EAAwC,6CAAA,6CAAA,CACvE,CAAC,CAAA;AACH,aAAA;YACDE,uBAAiB,CAAC,YAAY,CAAC,IAAI,EAAE,QAAQ,EAAEC,2BAAqB,CAAC,CAAC;YAChE,gBAAgB,GAAG,oBAAoB,CAAC,YAAY,CAAC,IAAI,EAAE,QAAQ,CAAC,CAAC;AACrE,YAAA,MAAM,GAAG,IAAI,cAAc,CAC/B,YAAY,CAAC,IAAI,EAEjB,gBAAA,uCAAA,QAAQ,EACR,gBAAgB,EAChB,YAAY,CACb,CAAC;AACF,YAAA,OAAA,CAAA,CAAA,aAAO,MAAM,CAAC,cAAc,EAAE,CAAC,CAAA;;;AAChC,CAAA;AAED;;;;;;;;;;;;;;;;;;;;;;;;AAwBG;SACmB,aAAa,CACjC,IAAU,EACV,QAAsB,EACtB,QAAgC,EAAA;;;;AAE1B,YAAA,YAAY,GAAGC,uBAAkB,CAAC,IAAI,CAAiB,CAAC;YAC9DF,uBAAiB,CAAC,YAAY,CAAC,IAAI,EAAE,QAAQ,EAAEC,2BAAqB,CAAC,CAAC;YAChE,gBAAgB,GAAG,oBAAoB,CAAC,YAAY,CAAC,IAAI,EAAE,QAAQ,CAAC,CAAC;AAErE,YAAA,MAAM,GAAG,IAAI,cAAc,CAC/B,YAAY,CAAC,IAAI,EAEjB,cAAA,qCAAA,QAAQ,EACR,gBAAgB,EAChB,YAAY,CACb,CAAC;AACF,YAAA,OAAA,CAAA,CAAA,aAAO,MAAM,CAAC,cAAc,EAAE,CAAC,CAAA;;;AAChC,CAAA;AAED;;;;AAIG;AACH,IAAA,cAAA,kBAAA,UAAA,MAAA,EAAA;IAA6BvB,eAA8B,CAAA,cAAA,EAAA,MAAA,CAAA,CAAA;IAOzD,SACE,cAAA,CAAA,IAAkB,EAClB,MAAqB,EACJ,QAAsB,EACvC,QAAuC,EACvC,IAAmB,EAAA;QALrB,IAOE,KAAA,GAAA,MAAA,CAAA,IAAA,CAAA,IAAA,EAAM,IAAI,EAAE,MAAM,EAAE,QAAQ,EAAE,IAAI,CAAC,IAMpC,IAAA,CAAA;QAVkB,KAAQ,CAAA,QAAA,GAAR,QAAQ,CAAc;QANjC,KAAU,CAAA,UAAA,GAAqB,IAAI,CAAC;QACpC,KAAM,CAAA,MAAA,GAAkB,IAAI,CAAC;QAUnC,IAAI,cAAc,CAAC,kBAAkB,EAAE;AACrC,YAAA,cAAc,CAAC,kBAAkB,CAAC,MAAM,EAAE,CAAC;AAC5C,SAAA;AAED,QAAA,cAAc,CAAC,kBAAkB,GAAG,KAAI,CAAC;;KAC1C;AAEK,IAAA,cAAA,CAAA,SAAA,CAAA,cAAc,GAApB,YAAA;;;;;AACiB,oBAAA,KAAA,CAAA,EAAA,OAAA,CAAA,CAAA,YAAM,IAAI,CAAC,OAAO,EAAE,CAAA,CAAA;;AAA7B,wBAAA,MAAM,GAAG,EAAoB,CAAA,IAAA,EAAA,CAAA;AACnC,wBAAAU,aAAO,CAAC,MAAM,EAAE,IAAI,CAAC,IAAI,sDAA+B,CAAC;AACzD,wBAAA,OAAA,CAAA,CAAA,aAAO,MAAM,CAAC,CAAA;;;;AACf,KAAA,CAAA;AAEK,IAAA,cAAA,CAAA,SAAA,CAAA,WAAW,GAAjB,YAAA;;;;;;;wBACEO,iBAAW,CACT,IAAI,CAAC,MAAM,CAAC,MAAM,KAAK,CAAC,EACxB,wCAAwC,CACzC,CAAC;wBACI,OAAO,GAAG,gBAAgB,EAAE,CAAC;AACnC,wBAAA,EAAA,GAAA,IAAI,CAAA;wBAAc,OAAM,CAAA,CAAA,YAAA,IAAI,CAAC,QAAQ,CAAC,UAAU,CAC9C,IAAI,CAAC,IAAI,EACT,IAAI,CAAC,QAAQ,EACb,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC;AACd,4BAAA,OAAO,CACR,CAAA,CAAA;;wBALD,EAAK,CAAA,UAAU,GAAG,EAAA,CAAA,IAAA,EAKjB,CAAC;AACF,wBAAA,IAAI,CAAC,UAAU,CAAC,eAAe,GAAG,OAAO,CAAC;;;;;;;;AAS1C,wBAAA,IAAI,CAAC,QAAQ,CAAC,iBAAiB,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,KAAK,CAAC,UAAA,CAAC,EAAA;AAChD,4BAAA,KAAI,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC;AACjB,yBAAC,CAAC,CAAC;wBAEH,IAAI,CAAC,QAAQ,CAAC,4BAA4B,CAAC,IAAI,CAAC,IAAI,EAAE,UAAA,WAAW,EAAA;4BAC/D,IAAI,CAAC,WAAW,EAAE;gCAChB,KAAI,CAAC,MAAM,CACTG,kBAAY,CAAC,KAAI,CAAC,IAAI,EAAwC,yBAAA,6CAAA,CAC/D,CAAC;AACH,6BAAA;AACH,yBAAC,CAAC,CAAC;;wBAGH,IAAI,CAAC,oBAAoB,EAAE,CAAC;;;;;AAC7B,KAAA,CAAA;AAED,IAAA,MAAA,CAAA,cAAA,CAAI,cAAO,CAAA,SAAA,EAAA,SAAA,EAAA;AAAX,QAAA,GAAA,EAAA,YAAA;;YACE,OAAO,CAAA,MAAA,IAAI,CAAC,UAAU,MAAE,IAAA,IAAA,EAAA,KAAA,KAAA,CAAA,GAAA,KAAA,CAAA,GAAA,EAAA,CAAA,eAAe,KAAI,IAAI,CAAC;SACjD;;;AAAA,KAAA,CAAA,CAAA;AAED,IAAA,cAAA,CAAA,SAAA,CAAA,MAAM,GAAN,YAAA;QACE,IAAI,CAAC,MAAM,CAACA,kBAAY,CAAC,IAAI,CAAC,IAAI,EAAsC,yBAAA,2CAAA,CAAC,CAAC;KAC3E,CAAA;AAED,IAAA,cAAA,CAAA,SAAA,CAAA,OAAO,GAAP,YAAA;QACE,IAAI,IAAI,CAAC,UAAU,EAAE;AACnB,YAAA,IAAI,CAAC,UAAU,CAAC,KAAK,EAAE,CAAC;AACzB,SAAA;QAED,IAAI,IAAI,CAAC,MAAM,EAAE;AACf,YAAA,MAAM,CAAC,YAAY,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;AAClC,SAAA;AAED,QAAA,IAAI,CAAC,UAAU,GAAG,IAAI,CAAC;AACvB,QAAA,IAAI,CAAC,MAAM,GAAG,IAAI,CAAC;AACnB,QAAA,cAAc,CAAC,kBAAkB,GAAG,IAAI,CAAC;KAC1C,CAAA;AAEO,IAAA,cAAA,CAAA,SAAA,CAAA,oBAAoB,GAA5B,YAAA;QAAA,IAqBC,KAAA,GAAA,IAAA,CAAA;AApBC,QAAA,IAAM,IAAI,GAAG,YAAA;;YACX,IAAI,CAAA,EAAA,GAAA,MAAA,KAAI,CAAC,UAAU,MAAE,IAAA,IAAA,EAAA,KAAA,KAAA,CAAA,GAAA,KAAA,CAAA,GAAA,EAAA,CAAA,MAAM,MAAE,IAAA,IAAA,EAAA,KAAA,KAAA,CAAA,GAAA,KAAA,CAAA,GAAA,EAAA,CAAA,MAAM,EAAE;;;;;;AAMnC,gBAAA,KAAI,CAAC,MAAM,GAAG,MAAM,CAAC,UAAU,CAAC,YAAA;AAC9B,oBAAA,KAAI,CAAC,MAAM,GAAG,IAAI,CAAC;oBACnB,KAAI,CAAC,MAAM,CACTA,kBAAY,CAAC,KAAI,CAAC,IAAI,EAAqC,sBAAA,0CAAA,CAC5D,CAAC;AACJ,iBAAC,iCAAsB,CAAC;gBACxB,OAAO;AACR,aAAA;AAED,YAAA,KAAI,CAAC,MAAM,GAAG,MAAM,CAAC,UAAU,CAAC,IAAI,EAAE,0BAA0B,CAAC,GAAG,EAAE,CAAC,CAAC;AAC1E,SAAC,CAAC;AAEF,QAAA,IAAI,EAAE,CAAC;KACR,CAAA;;;IAzGc,cAAkB,CAAA,kBAAA,GAA0B,IAAI,CAAC;IA0GlE,OAAC,cAAA,CAAA;CAAA,CA7G4B,8BAA8B,CA6G1D,CAAA;;ACjUD;;;;;;;;;;;;;;;AAeG;AAcH,IAAM,oBAAoB,GAAG,iBAAiB,CAAC;AAE/C;AACA;AACA,IAAM,kBAAkB,GAGpB,IAAI,GAAG,EAAE,CAAC;AAEd,IAAA,cAAA,kBAAA,UAAA,MAAA,EAAA;IAAoCpB,eAA8B,CAAA,cAAA,EAAA,MAAA,CAAA,CAAA;AAGhE,IAAA,SAAA,cAAA,CACE,IAAkB,EAClB,QAAuC,EACvC,eAAuB,EAAA;AAAvB,QAAA,IAAA,eAAA,KAAA,KAAA,CAAA,EAAA,EAAA,eAAuB,GAAA,KAAA,CAAA,EAAA;QAHzB,IAKE,KAAA,GAAA,MAAA,CAAA,IAAA,CAAA,IAAA,EACE,IAAI,EACJ;;;;;AAKC,SAAA,EACD,QAAQ,EACR,SAAS,EACT,eAAe,CAChB,IACF,IAAA,CAAA;QAnBD,KAAO,CAAA,OAAA,GAAG,IAAI,CAAC;;KAmBd;AAED;;;AAGG;AACG,IAAA,cAAA,CAAA,SAAA,CAAA,OAAO,GAAb,YAAA;;;;;;AACM,wBAAA,YAAY,GAAG,kBAAkB,CAAC,GAAG,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,EAAE,CAAC,CAAC;6BACxD,CAAC,YAAY,EAAb,OAAa,CAAA,CAAA,YAAA,CAAA,CAAA,CAAA;;;;wBAEc,OAAM,CAAA,CAAA,YAAA,iCAAiC,CAChE,IAAI,CAAC,QAAQ,EACb,IAAI,CAAC,IAAI,CACV,CAAA,CAAA;;AAHK,wBAAA,kBAAkB,GAAG,EAG1B,CAAA,IAAA,EAAA,CAAA;AACc,wBAAA,IAAA,CAAA,kBAAkB,EAAlB,OAAkB,CAAA,CAAA,YAAA,CAAA,CAAA,CAAA;wBAAG,OAAM,CAAA,CAAA,YAAA,MAAA,CAAA,SAAA,CAAM,OAAO,CAAA,IAAA,CAAA,IAAA,CAAE,CAAA,CAAA;;AAArB,wBAAA,EAAA,GAAA,SAAqB,CAAA;;;AAAG,wBAAA,EAAA,GAAA,IAAI,CAAA;;;wBAA1D,QAA0D,GAAA,EAAA,CAAA;wBAChE,YAAY,GAAG,YAAM,EAAA,OAAA,OAAO,CAAC,OAAO,CAAC,QAAM,CAAC,CAAvB,EAAuB,CAAC;;;;wBAE7C,YAAY,GAAG,YAAM,EAAA,OAAA,OAAO,CAAC,MAAM,CAAC,GAAC,CAAC,CAAjB,EAAiB,CAAC;;;AAGzC,wBAAA,kBAAkB,CAAC,GAAG,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,EAAE,EAAE,YAAY,CAAC,CAAC;;;;;AAKzD,wBAAA,IAAI,CAAC,IAAI,CAAC,eAAe,EAAE;4BACzB,kBAAkB,CAAC,GAAG,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,EAAE,EAAE,cAAM,OAAA,OAAO,CAAC,OAAO,CAAC,IAAI,CAAC,CAAA,EAAA,CAAC,CAAC;AACvE,yBAAA;wBAED,OAAO,CAAA,CAAA,aAAA,YAAY,EAAE,CAAC,CAAA;;;;AACvB,KAAA,CAAA;IAEK,cAAW,CAAA,SAAA,CAAA,WAAA,GAAjB,UAAkB,KAAgB,EAAA;;;;;;AAChC,wBAAA,IAAI,KAAK,CAAC,IAAI,KAAA,mBAAA,2CAAyC;AACrD,4BAAA,OAAA,CAAA,CAAA,aAAO,MAAM,CAAA,SAAA,CAAA,WAAW,CAAC,IAAA,CAAA,IAAA,EAAA,KAAK,CAAC,CAAC,CAAA;AACjC,yBAAA;AAAM,6BAAA,IAAI,KAAK,CAAC,IAAI,KAAA,SAAA,8BAA4B;;AAE/C,4BAAA,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC;4BACnB,OAAO,CAAA,CAAA,YAAA,CAAA;AACR,yBAAA;6BAEG,KAAK,CAAC,OAAO,EAAb,OAAa,CAAA,CAAA,YAAA,CAAA,CAAA,CAAA;wBACF,OAAM,CAAA,CAAA,YAAA,IAAI,CAAC,IAAI,CAAC,kBAAkB,CAAC,KAAK,CAAC,OAAO,CAAC,CAAA,CAAA;;AAAxD,wBAAA,IAAI,GAAG,EAAiD,CAAA,IAAA,EAAA,CAAA;AAC9D,wBAAA,IAAI,IAAI,EAAE;AACR,4BAAA,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC;AACjB,4BAAA,OAAA,CAAA,CAAA,aAAO,MAAM,CAAA,SAAA,CAAA,WAAW,CAAC,IAAA,CAAA,IAAA,EAAA,KAAK,CAAC,CAAC,CAAA;AACjC,yBAAA;AAAM,6BAAA;AACL,4BAAA,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC;AACpB,yBAAA;;;;;;AAEJ,KAAA,CAAA;AAEK,IAAA,cAAA,CAAA,SAAA,CAAA,WAAW,GAAjB,YAAA;;;;AAAqC,KAAA,CAAA;IAErC,cAAO,CAAA,SAAA,CAAA,OAAA,GAAP,eAAkB,CAAA;IACpB,OAAC,cAAA,CAAA;AAAD,CA3EA,CAAoC,8BAA8B,CA2EjE,CAAA,CAAA;AAEqB,SAAA,iCAAiC,CACrD,QAAuC,EACvC,IAAkB,EAAA;;;;;;AAEZ,oBAAA,GAAG,GAAG,kBAAkB,CAAC,IAAI,CAAC,CAAC;AAC/B,oBAAA,WAAW,GAAG,mBAAmB,CAAC,QAAQ,CAAC,CAAC;AAC5C,oBAAA,OAAA,CAAA,CAAA,YAAM,WAAW,CAAC,YAAY,EAAE,CAAA,CAAA;;AAAtC,oBAAA,IAAI,EAAE,EAAgC,CAAA,IAAA,EAAA,CAAC,EAAE;AACvC,wBAAA,OAAA,CAAA,CAAA,aAAO,KAAK,CAAC,CAAA;AACd,qBAAA;AAC2B,oBAAA,OAAA,CAAA,CAAA,YAAM,WAAW,CAAC,IAAI,CAAC,GAAG,CAAC,CAAA,CAAA;;AAAjD,oBAAA,kBAAkB,GAAG,CAAC,EAA2B,CAAA,IAAA,EAAA,MAAM,MAAM,CAAA;AACnE,oBAAA,OAAA,CAAA,CAAA,YAAM,WAAW,CAAC,OAAO,CAAC,GAAG,CAAC,CAAA,CAAA;;AAA9B,oBAAA,EAAA,CAAA,IAAA,EAA8B,CAAC;AAC/B,oBAAA,OAAA,CAAA,CAAA,aAAO,kBAAkB,CAAC,CAAA;;;;AAC3B,CAAA;AAEqB,SAAA,yBAAyB,CAC7C,QAAuC,EACvC,IAAkB,EAAA;;;AAElB,YAAA,OAAA,CAAA,CAAA,aAAO,mBAAmB,CAAC,QAAQ,CAAC,CAAC,IAAI,CAAC,kBAAkB,CAAC,IAAI,CAAC,EAAE,MAAM,CAAC,CAAC,CAAA;;;AAC7E,CAAA;SAEe,sBAAsB,GAAA;IACpC,kBAAkB,CAAC,KAAK,EAAE,CAAC;AAC7B,CAAC;AAEe,SAAA,uBAAuB,CACrC,IAAkB,EAClB,MAAoD,EAAA;IAEpD,kBAAkB,CAAC,GAAG,CAAC,IAAI,CAAC,IAAI,EAAE,EAAE,MAAM,CAAC,CAAC;AAC9C,CAAC;AAED,SAAS,mBAAmB,CAC1B,QAAuC,EAAA;AAEvC,IAAA,OAAOS,kBAAY,CAAC,QAAQ,CAAC,oBAAoB,CAAC,CAAC;AACrD,CAAC;AAED,SAAS,kBAAkB,CAAC,IAAkB,EAAA;AAC5C,IAAA,OAAOgB,yBAAmB,CACxB,oBAAoB,EACpB,IAAI,CAAC,MAAM,CAAC,MAAM,EAClB,IAAI,CAAC,IAAI,CACV,CAAC;AACJ;;AC/JA;;;;;;;;;;;;;;;AAeG;AA4BH;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA2CG;SACa,kBAAkB,CAChC,IAAU,EACV,QAAsB,EACtB,QAAgC,EAAA;IAEhC,OAAO,mBAAmB,CAAC,IAAI,EAAE,QAAQ,EAAE,QAAQ,CAAmB,CAAC;AACzE,CAAC;SAEqB,mBAAmB,CACvC,IAAU,EACV,QAAsB,EACtB,QAAgC,EAAA;;;;;;AAEhC,oBAAA,IAAIN,wBAAoB,CAAC,IAAI,CAAC,GAAG,CAAC,EAAE;wBAClC,OAAO,CAAA,CAAA,aAAA,OAAO,CAAC,MAAM,CACnBO,qDAA+C,CAAC,IAAI,CAAC,CACtD,CAAC,CAAA;AACH,qBAAA;AACK,oBAAA,YAAY,GAAGL,eAAS,CAAC,IAAI,CAAC,CAAC;AACrC,oBAAAC,uBAAiB,CAAC,IAAI,EAAE,QAAQ,EAAEC,2BAAqB,CAAC,CAAC;;;;oBAIzD,OAAM,CAAA,CAAA,YAAA,YAAY,CAAC,sBAAsB,CAAA,CAAA;;;;;AAAzC,oBAAA,EAAA,CAAA,IAAA,EAAyC,CAAC;AACpC,oBAAA,gBAAgB,GAAG,oBAAoB,CAAC,YAAY,EAAE,QAAQ,CAAC,CAAC;AACtE,oBAAA,OAAA,CAAA,CAAA,YAAM,yBAAyB,CAAC,gBAAgB,EAAE,YAAY,CAAC,CAAA,CAAA;;AAA/D,oBAAA,EAAA,CAAA,IAAA,EAA+D,CAAC;oBAEhE,OAAO,CAAA,CAAA,aAAA,gBAAgB,CAAC,aAAa,CACnC,YAAY,EACZ,QAAQ,+DAET,CAAC,CAAA;;;;AACH,CAAA;AAED;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAkCG;SACa,0BAA0B,CACxC,IAAU,EACV,QAAsB,EACtB,QAAgC,EAAA;IAEhC,OAAO,2BAA2B,CAChC,IAAI,EACJ,QAAQ,EACR,QAAQ,CACS,CAAC;AACtB,CAAC;SACqB,2BAA2B,CAC/C,IAAU,EACV,QAAsB,EACtB,QAAgC,EAAA;;;;;;AAE1B,oBAAA,YAAY,GAAGC,uBAAkB,CAAC,IAAI,CAAiB,CAAC;oBAC9DF,uBAAiB,CAAC,YAAY,CAAC,IAAI,EAAE,QAAQ,EAAEC,2BAAqB,CAAC,CAAC;oBACtE,IAAIJ,wBAAoB,CAAC,YAAY,CAAC,IAAI,CAAC,GAAG,CAAC,EAAE;wBAC/C,OAAO,CAAA,CAAA,aAAA,OAAO,CAAC,MAAM,CACnBO,qDAA+C,CAAC,YAAY,CAAC,IAAI,CAAC,CACnE,CAAC,CAAA;AACH,qBAAA;;;;AAID,oBAAA,OAAA,CAAA,CAAA,YAAM,YAAY,CAAC,IAAI,CAAC,sBAAsB,CAAA,CAAA;;;;;AAA9C,oBAAA,EAAA,CAAA,IAAA,EAA8C,CAAC;oBAEzC,gBAAgB,GAAG,oBAAoB,CAAC,YAAY,CAAC,IAAI,EAAE,QAAQ,CAAC,CAAC;oBAC3E,OAAM,CAAA,CAAA,YAAA,yBAAyB,CAAC,gBAAgB,EAAE,YAAY,CAAC,IAAI,CAAC,CAAA,CAAA;;AAApE,oBAAA,EAAA,CAAA,IAAA,EAAoE,CAAC;AAErD,oBAAA,OAAA,CAAA,CAAA,YAAM,sBAAsB,CAAC,YAAY,CAAC,CAAA,CAAA;;AAApD,oBAAA,OAAO,GAAG,EAA0C,CAAA,IAAA,EAAA,CAAA;AAC1D,oBAAA,OAAA,CAAA,CAAA,aAAO,gBAAgB,CAAC,aAAa,CACnC,YAAY,CAAC,IAAI,EACjB,QAAQ,EAAA,mBAAA,0CAER,OAAO,CACR,CAAC,CAAA;;;;AACH,CAAA;AAED;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA8BG;SACa,gBAAgB,CAC9B,IAAU,EACV,QAAsB,EACtB,QAAgC,EAAA;IAEhC,OAAO,iBAAiB,CAAC,IAAI,EAAE,QAAQ,EAAE,QAAQ,CAAmB,CAAC;AACvE,CAAC;SACqB,iBAAiB,CACrC,IAAU,EACV,QAAsB,EACtB,QAAgC,EAAA;;;;;;AAE1B,oBAAA,YAAY,GAAGF,uBAAkB,CAAC,IAAI,CAAiB,CAAC;oBAC9DF,uBAAiB,CAAC,YAAY,CAAC,IAAI,EAAE,QAAQ,EAAEC,2BAAqB,CAAC,CAAC;;;;AAItE,oBAAA,OAAA,CAAA,CAAA,YAAM,YAAY,CAAC,IAAI,CAAC,sBAAsB,CAAA,CAAA;;;;;AAA9C,oBAAA,EAAA,CAAA,IAAA,EAA8C,CAAC;oBAEzC,gBAAgB,GAAG,oBAAoB,CAAC,YAAY,CAAC,IAAI,EAAE,QAAQ,CAAC,CAAC;oBAC3E,OAAM,CAAA,CAAA,YAAAI,yBAAmB,CAAC,KAAK,EAAE,YAAY,EAAE,QAAQ,CAAC,UAAU,CAAC,CAAA,CAAA;;AAAnE,oBAAA,EAAA,CAAA,IAAA,EAAmE,CAAC;oBACpE,OAAM,CAAA,CAAA,YAAA,yBAAyB,CAAC,gBAAgB,EAAE,YAAY,CAAC,IAAI,CAAC,CAAA,CAAA;;AAApE,oBAAA,EAAA,CAAA,IAAA,EAAoE,CAAC;AAErD,oBAAA,OAAA,CAAA,CAAA,YAAM,sBAAsB,CAAC,YAAY,CAAC,CAAA,CAAA;;AAApD,oBAAA,OAAO,GAAG,EAA0C,CAAA,IAAA,EAAA,CAAA;AAC1D,oBAAA,OAAA,CAAA,CAAA,aAAO,gBAAgB,CAAC,aAAa,CACnC,YAAY,CAAC,IAAI,EACjB,QAAQ,EAAA,iBAAA,wCAER,OAAO,CACR,CAAC,CAAA;;;;AACH,CAAA;AAED;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAwCG;AACmB,SAAA,iBAAiB,CACrC,IAAU,EACV,QAAgC,EAAA;;;;AAEhC,gBAAA,KAAA,CAAA,EAAA,OAAA,CAAA,CAAA,YAAMN,eAAS,CAAC,IAAI,CAAC,CAAC,sBAAsB,CAAA,CAAA;;AAA5C,oBAAA,EAAA,CAAA,IAAA,EAA4C,CAAC;oBAC7C,OAAO,CAAA,CAAA,aAAA,kBAAkB,CAAC,IAAI,EAAE,QAAQ,EAAE,KAAK,CAAC,CAAC,CAAA;;;;AAClD,CAAA;SAEqB,kBAAkB,CACtC,IAAU,EACV,cAAsC,EACtC,eAAuB,EAAA;AAAvB,IAAA,IAAA,eAAA,KAAA,KAAA,CAAA,EAAA,EAAA,eAAuB,GAAA,KAAA,CAAA,EAAA;;;;;;AAEvB,oBAAA,IAAIF,wBAAoB,CAAC,IAAI,CAAC,GAAG,CAAC,EAAE;wBAClC,OAAO,CAAA,CAAA,aAAA,OAAO,CAAC,MAAM,CACnBO,qDAA+C,CAAC,IAAI,CAAC,CACtD,CAAC,CAAA;AACH,qBAAA;AACK,oBAAA,YAAY,GAAGL,eAAS,CAAC,IAAI,CAAC,CAAC;AAC/B,oBAAA,QAAQ,GAAG,oBAAoB,CAAC,YAAY,EAAE,cAAc,CAAC,CAAC;oBAC9D,MAAM,GAAG,IAAI,cAAc,CAAC,YAAY,EAAE,QAAQ,EAAE,eAAe,CAAC,CAAC;AAC5D,oBAAA,OAAA,CAAA,CAAA,YAAM,MAAM,CAAC,OAAO,EAAE,CAAA,CAAA;;AAA/B,oBAAA,MAAM,GAAG,EAAsB,CAAA,IAAA,EAAA,CAAA;AAEjC,oBAAA,IAAA,EAAA,MAAM,IAAI,CAAC,eAAe,CAAA,EAA1B,OAA0B,CAAA,CAAA,YAAA,CAAA,CAAA,CAAA;AAC5B,oBAAA,OAAO,MAAM,CAAC,IAAI,CAAC,gBAAgB,CAAC;oBACpC,OAAM,CAAA,CAAA,YAAA,YAAY,CAAC,qBAAqB,CAAC,MAAM,CAAC,IAAoB,CAAC,CAAA,CAAA;;AAArE,oBAAA,EAAA,CAAA,IAAA,EAAqE,CAAC;oBACtE,OAAM,CAAA,CAAA,YAAA,YAAY,CAAC,gBAAgB,CAAC,IAAI,EAAE,cAAc,CAAC,CAAA,CAAA;;AAAzD,oBAAA,EAAA,CAAA,IAAA,EAAyD,CAAC;;AAG5D,gBAAA,KAAA,CAAA,EAAA,OAAA,CAAA,CAAA,aAAO,MAAM,CAAC,CAAA;;;;AACf,CAAA;AAED,SAAe,sBAAsB,CAAC,IAAkB,EAAA;;;;;;oBAChD,OAAO,GAAG,gBAAgB,CAAC,EAAA,CAAA,MAAA,CAAG,IAAI,CAAC,GAAG,EAAK,KAAA,CAAA,CAAC,CAAC;AACnD,oBAAA,IAAI,CAAC,gBAAgB,GAAG,OAAO,CAAC;oBAChC,OAAM,CAAA,CAAA,YAAA,IAAI,CAAC,IAAI,CAAC,gBAAgB,CAAC,IAAI,CAAC,CAAA,CAAA;;AAAtC,oBAAA,EAAA,CAAA,IAAA,EAAsC,CAAC;oBACvC,OAAM,CAAA,CAAA,YAAA,IAAI,CAAC,IAAI,CAAC,qBAAqB,CAAC,IAAI,CAAC,CAAA,CAAA;;AAA3C,oBAAA,EAAA,CAAA,IAAA,EAA2C,CAAC;AAC5C,oBAAA,OAAA,CAAA,CAAA,aAAO,OAAO,CAAC,CAAA;;;;AAChB;;AClVD;;;;;;;;;;;;;;;AAeG;AAYH;AACA;AACA,IAAM,mCAAmC,GAAG,EAAE,GAAG,EAAE,GAAG,IAAI,CAAC;AAE3D,IAAA,gBAAA,kBAAA,YAAA;AAOE,IAAA,SAAA,gBAAA,CAA6B,IAAkB,EAAA;QAAlB,IAAI,CAAA,IAAA,GAAJ,IAAI,CAAc;AAN9B,QAAA,IAAA,CAAA,eAAe,GAAgB,IAAI,GAAG,EAAE,CAAC;AACzC,QAAA,IAAA,CAAA,SAAS,GAA2B,IAAI,GAAG,EAAE,CAAC;QACrD,IAAmB,CAAA,mBAAA,GAAqB,IAAI,CAAC;QAC7C,IAA2B,CAAA,2BAAA,GAAG,KAAK,CAAC;AACtC,QAAA,IAAA,CAAA,sBAAsB,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC;KAEO;IAEnD,gBAAgB,CAAA,SAAA,CAAA,gBAAA,GAAhB,UAAiB,iBAAoC,EAAA;AACnD,QAAA,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC,iBAAiB,CAAC,CAAC;QAEtC,IACE,IAAI,CAAC,mBAAmB;YACxB,IAAI,CAAC,kBAAkB,CAAC,IAAI,CAAC,mBAAmB,EAAE,iBAAiB,CAAC,EACpE;YACA,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,mBAAmB,EAAE,iBAAiB,CAAC,CAAC;AACjE,YAAA,IAAI,CAAC,gBAAgB,CAAC,IAAI,CAAC,mBAAmB,CAAC,CAAC;AAChD,YAAA,IAAI,CAAC,mBAAmB,GAAG,IAAI,CAAC;AACjC,SAAA;KACF,CAAA;IAED,gBAAkB,CAAA,SAAA,CAAA,kBAAA,GAAlB,UAAmB,iBAAoC,EAAA;AACrD,QAAA,IAAI,CAAC,SAAS,CAAC,MAAM,CAAC,iBAAiB,CAAC,CAAC;KAC1C,CAAA;IAED,gBAAO,CAAA,SAAA,CAAA,OAAA,GAAP,UAAQ,KAAgB,EAAA;QAAxB,IA8BC,KAAA,GAAA,IAAA,CAAA;;AA5BC,QAAA,IAAI,IAAI,CAAC,mBAAmB,CAAC,KAAK,CAAC,EAAE;AACnC,YAAA,OAAO,KAAK,CAAC;AACd,SAAA;QAED,IAAI,OAAO,GAAG,KAAK,CAAC;AACpB,QAAA,IAAI,CAAC,SAAS,CAAC,OAAO,CAAC,UAAA,QAAQ,EAAA;YAC7B,IAAI,KAAI,CAAC,kBAAkB,CAAC,KAAK,EAAE,QAAQ,CAAC,EAAE;gBAC5C,OAAO,GAAG,IAAI,CAAC;AACf,gBAAA,KAAI,CAAC,cAAc,CAAC,KAAK,EAAE,QAAQ,CAAC,CAAC;AACrC,gBAAA,KAAI,CAAC,gBAAgB,CAAC,KAAK,CAAC,CAAC;AAC9B,aAAA;AACH,SAAC,CAAC,CAAC;QAEH,IAAI,IAAI,CAAC,2BAA2B,IAAI,CAAC,eAAe,CAAC,KAAK,CAAC,EAAE;;;AAG/D,YAAA,OAAO,OAAO,CAAC;AAChB,SAAA;AAED,QAAA,IAAI,CAAC,2BAA2B,GAAG,IAAI,CAAC;;QAGxC,IAAI,CAAC,OAAO,EAAE;AACZ,YAAA,IAAI,CAAC,mBAAmB,GAAG,KAAK,CAAC;YACjC,OAAO,GAAG,IAAI,CAAC;AAChB,SAAA;AAED,QAAA,OAAO,OAAO,CAAC;KAChB,CAAA;AAEO,IAAA,gBAAA,CAAA,SAAA,CAAA,cAAc,GAAtB,UAAuB,KAAgB,EAAE,QAA2B,EAAA;;QAClE,IAAI,KAAK,CAAC,KAAK,IAAI,CAAC,mBAAmB,CAAC,KAAK,CAAC,EAAE;AAC9C,YAAA,IAAM,IAAI,GACR,CAAC,CAAA,EAAA,GAAA,KAAK,CAAC,KAAK,CAAC,IAAI,0CAAE,KAAK,CAAC,OAAO,CAAA,CAAE,CAAC,CAAmB;oEACzB;AAC/B,YAAA,QAAQ,CAAC,OAAO,CAACD,kBAAY,CAAC,IAAI,CAAC,IAAI,EAAE,IAAI,CAAC,CAAC,CAAC;AACjD,SAAA;AAAM,aAAA;AACL,YAAA,QAAQ,CAAC,WAAW,CAAC,KAAK,CAAC,CAAC;AAC7B,SAAA;KACF,CAAA;AAEO,IAAA,gBAAA,CAAA,SAAA,CAAA,kBAAkB,GAA1B,UACE,KAAgB,EAChB,QAA2B,EAAA;AAE3B,QAAA,IAAM,cAAc,GAClB,QAAQ,CAAC,OAAO,KAAK,IAAI;AACzB,aAAC,CAAC,CAAC,KAAK,CAAC,OAAO,IAAI,KAAK,CAAC,OAAO,KAAK,QAAQ,CAAC,OAAO,CAAC,CAAC;AAC1D,QAAA,OAAO,QAAQ,CAAC,MAAM,CAAC,QAAQ,CAAC,KAAK,CAAC,IAAI,CAAC,IAAI,cAAc,CAAC;KAC/D,CAAA;IAEO,gBAAmB,CAAA,SAAA,CAAA,mBAAA,GAA3B,UAA4B,KAAgB,EAAA;AAC1C,QAAA,IACE,IAAI,CAAC,GAAG,EAAE,GAAG,IAAI,CAAC,sBAAsB;AACxC,YAAA,mCAAmC,EACnC;AACA,YAAA,IAAI,CAAC,eAAe,CAAC,KAAK,EAAE,CAAC;AAC9B,SAAA;QAED,OAAO,IAAI,CAAC,eAAe,CAAC,GAAG,CAAC,QAAQ,CAAC,KAAK,CAAC,CAAC,CAAC;KAClD,CAAA;IAEO,gBAAgB,CAAA,SAAA,CAAA,gBAAA,GAAxB,UAAyB,KAAgB,EAAA;QACvC,IAAI,CAAC,eAAe,CAAC,GAAG,CAAC,QAAQ,CAAC,KAAK,CAAC,CAAC,CAAC;AAC1C,QAAA,IAAI,CAAC,sBAAsB,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC;KAC1C,CAAA;IACH,OAAC,gBAAA,CAAA;AAAD,CAAC,EAAA,CAAA,CAAA;AAED,SAAS,QAAQ,CAAC,CAAY,EAAA;AAC5B,IAAA,OAAO,CAAC,CAAC,CAAC,IAAI,EAAE,CAAC,CAAC,OAAO,EAAE,CAAC,CAAC,SAAS,EAAE,CAAC,CAAC,QAAQ,CAAC,CAAC,MAAM,CAAC,UAAA,CAAC,IAAI,OAAA,CAAC,CAAD,EAAC,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;AAC/E,CAAC;AAED,SAAS,mBAAmB,CAAC,EAA0B,EAAA;QAAxB,IAAI,GAAA,EAAA,CAAA,IAAA,EAAE,KAAK,GAAA,EAAA,CAAA,KAAA,CAAA;IACxC,QACE,IAAI,KAA0B,SAAA;QAC9B,CAAA,KAAK,KAAL,IAAA,IAAA,KAAK,KAAL,KAAA,CAAA,GAAA,KAAA,CAAA,GAAA,KAAK,CAAE,IAAI,MAAK,OAAqC,CAAA,MAAA,CAAA,eAAA,mCAAA,EACrD;AACJ,CAAC;AAED,SAAS,eAAe,CAAC,KAAgB,EAAA;IACvC,QAAQ,KAAK,CAAC,IAAI;QAChB,KAAwC,mBAAA,0CAAA;QACxC,KAAqC,iBAAA,uCAAA;AACrC,QAAA,KAAA,mBAAA;AACE,YAAA,OAAO,IAAI,CAAC;AACd,QAAA,KAAA,SAAA;AACE,YAAA,OAAO,mBAAmB,CAAC,KAAK,CAAC,CAAC;AACpC,QAAA;AACE,YAAA,OAAO,KAAK,CAAC;AAChB,KAAA;AACH;;ACrJA;;;;;;;;;;;;;;;AAeG;AAcmB,SAAA,iBAAiB,CACrC,IAAU,EACV,OAAqC,EAAA;AAArC,IAAA,IAAA,OAAA,KAAA,KAAA,CAAA,EAAA,EAAA,OAAqC,GAAA,EAAA,CAAA,EAAA;;;AAErC,YAAA,OAAA,CAAA,CAAA,aAAOQ,wBAAkB,CACvB,IAAI,EAGJ,KAAA,uBAAA,cAAA,oCAAA,OAAO,CACR,CAAC,CAAA;;;AACH;;ACvCD;;;;;;;;;;;;;;;AAeG;AAQH,IAAM,gBAAgB,GAAG,sCAAsC,CAAC;AAChE,IAAM,UAAU,GAAG,SAAS,CAAC;AAEvB,SAAgBC,iBAAe,CAAC,IAAkB,EAAA;;;;;;;AAEtD,oBAAA,IAAI,IAAI,CAAC,MAAM,CAAC,QAAQ,EAAE;wBACxB,OAAO,CAAA,CAAA,YAAA,CAAA;AACR,qBAAA;AAE6B,oBAAA,OAAA,CAAA,CAAA,YAAM,iBAAiB,CAAC,IAAI,CAAC,CAAA,CAAA;;AAAnD,oBAAA,iBAAiB,GAAK,CAAA,EAA6B,CAAA,IAAA,EAAA,EAAlC,iBAAA,CAAA;AAEzB,oBAAA,KAAA,EAAA,GAAA,CAAsC,EAAjB,mBAAiB,GAAA,iBAAA,EAAjB,EAAiB,GAAA,mBAAA,CAAA,MAAA,EAAjB,IAAiB,EAAE;wBAA7B,MAAM,GAAA,mBAAA,CAAA,EAAA,CAAA,CAAA;wBACf,IAAI;AACF,4BAAA,IAAI,WAAW,CAAC,MAAM,CAAC,EAAE;gCACvB,OAAO,CAAA,CAAA,YAAA,CAAA;AACR,6BAAA;AACF,yBAAA;wBAAC,OAAM,EAAA,EAAA;;AAEP,yBAAA;AACF,qBAAA;;oBAGDb,WAAK,CAAC,IAAI,EAAA,qBAAA,oCAA+B,CAAC;;;;;AAC3C,CAAA;AAED,SAAS,WAAW,CAAC,QAAgB,EAAA;AACnC,IAAA,IAAM,UAAU,GAAGc,oBAAc,EAAE,CAAC;AAC9B,IAAA,IAAA,EAAyB,GAAA,IAAI,GAAG,CAAC,UAAU,CAAC,EAA1C,QAAQ,GAAA,EAAA,CAAA,QAAA,EAAE,QAAQ,GAAA,EAAA,CAAA,QAAwB,CAAC;AACnD,IAAA,IAAI,QAAQ,CAAC,UAAU,CAAC,qBAAqB,CAAC,EAAE;AAC9C,QAAA,IAAM,KAAK,GAAG,IAAI,GAAG,CAAC,QAAQ,CAAC,CAAC;QAEhC,IAAI,KAAK,CAAC,QAAQ,KAAK,EAAE,IAAI,QAAQ,KAAK,EAAE,EAAE;;YAE5C,QACE,QAAQ,KAAK,mBAAmB;AAChC,gBAAA,QAAQ,CAAC,OAAO,CAAC,qBAAqB,EAAE,EAAE,CAAC;oBACzC,UAAU,CAAC,OAAO,CAAC,qBAAqB,EAAE,EAAE,CAAC,EAC/C;AACH,SAAA;QAED,OAAO,QAAQ,KAAK,mBAAmB,IAAI,KAAK,CAAC,QAAQ,KAAK,QAAQ,CAAC;AACxE,KAAA;AAED,IAAA,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,QAAQ,CAAC,EAAE;AAC9B,QAAA,OAAO,KAAK,CAAC;AACd,KAAA;AAED,IAAA,IAAI,gBAAgB,CAAC,IAAI,CAAC,QAAQ,CAAC,EAAE;;;QAGnC,OAAO,QAAQ,KAAK,QAAQ,CAAC;AAC9B,KAAA;;IAGD,IAAM,oBAAoB,GAAG,QAAQ,CAAC,OAAO,CAAC,KAAK,EAAE,KAAK,CAAC,CAAC;;;AAG5D,IAAA,IAAM,EAAE,GAAG,IAAI,MAAM,CACnB,SAAS,GAAG,oBAAoB,GAAG,GAAG,GAAG,oBAAoB,GAAG,IAAI,EACpE,GAAG,CACJ,CAAC;AACF,IAAA,OAAO,EAAE,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;AAC3B;;ACrFA;;;;;;;;;;;;;;;AAeG;AASH,IAAM,eAAe,GAAG,IAAIZ,WAAK,CAAC,KAAK,EAAE,KAAK,CAAC,CAAC;AAEhD;;;AAGG;AACH,SAAS,wBAAwB,GAAA;;;;AAI/B,IAAA,IAAM,MAAM,GAAGa,aAAO,EAAE,CAAC,MAAM,CAAC;;AAEhC,IAAA,IAAI,MAAM,KAAN,IAAA,IAAA,MAAM,uBAAN,MAAM,CAAE,CAAC,EAAE;;AAEb,QAAA,KAAmB,IAAqB,EAAA,GAAA,CAAA,EAArB,EAAA,GAAA,MAAM,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC,EAArB,EAAqB,GAAA,EAAA,CAAA,MAAA,EAArB,IAAqB,EAAE;AAArC,YAAA,IAAM,IAAI,GAAA,EAAA,CAAA,EAAA,CAAA,CAAA;;AAEb,YAAA,MAAM,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,GAAG,MAAM,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,IAAI,EAAE,CAAC;;AAE1C,YAAA,MAAM,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,GAAG,MAAM,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,IAAI,EAAE,CAAC;;AAE1C,YAAA,MAAM,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,GAAAC,mBAAA,CAAA,EAAA,EAAO,MAAM,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,OAAC,CAAC;;YAEzC,IAAI,MAAM,CAAC,EAAE,EAAE;AACb,gBAAA,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,MAAM,CAAC,EAAE,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;;AAEzC,oBAAA,MAAM,CAAC,EAAE,CAAC,CAAC,CAAC,GAAG,IAAI,CAAC;AACrB,iBAAA;AACF,aAAA;AACF,SAAA;AACF,KAAA;AACH,CAAC;AAED,SAAS,QAAQ,CAAC,IAAkB,EAAA;AAClC,IAAA,OAAO,IAAI,OAAO,CAAuB,UAAC,OAAO,EAAE,MAAM,EAAA;;;AAEvD,QAAA,SAAS,cAAc,GAAA;;;AAGrB,YAAA,wBAAwB,EAAE,CAAC;AAC3B,YAAA,IAAI,CAAC,IAAI,CAAC,cAAc,EAAE;AACxB,gBAAA,QAAQ,EAAE,YAAA;oBACR,OAAO,CAAC,IAAI,CAAC,OAAO,CAAC,UAAU,EAAE,CAAC,CAAC;iBACpC;AACD,gBAAA,SAAS,EAAE,YAAA;;;;;;;AAOT,oBAAA,wBAAwB,EAAE,CAAC;AAC3B,oBAAA,MAAM,CAACZ,kBAAY,CAAC,IAAI,EAAA,wBAAA,4CAAuC,CAAC,CAAC;iBAClE;AACD,gBAAA,OAAO,EAAE,eAAe,CAAC,GAAG,EAAE;AAC/B,aAAA,CAAC,CAAC;SACJ;QAED,IAAI,CAAA,EAAA,GAAA,CAAA,EAAA,GAAAW,aAAO,EAAE,CAAC,IAAI,MAAE,IAAA,IAAA,EAAA,KAAA,KAAA,CAAA,GAAA,KAAA,CAAA,GAAA,EAAA,CAAA,OAAO,MAAE,IAAA,IAAA,EAAA,KAAA,KAAA,CAAA,GAAA,KAAA,CAAA,GAAA,EAAA,CAAA,MAAM,EAAE;;YAEnC,OAAO,CAAC,IAAI,CAAC,OAAO,CAAC,UAAU,EAAE,CAAC,CAAC;AACpC,SAAA;aAAM,IAAI,CAAC,EAAC,CAAA,EAAA,GAAAA,aAAO,EAAE,CAAC,IAAI,MAAA,IAAA,IAAA,EAAA,KAAA,KAAA,CAAA,GAAA,KAAA,CAAA,GAAA,EAAA,CAAE,IAAI,CAAA,EAAE;;AAEjC,YAAA,cAAc,EAAE,CAAC;AAClB,SAAA;AAAM,aAAA;;;;;;YAML,IAAM,MAAM,GAAGE,2BAAwB,CAAC,WAAW,CAAC,CAAC;;AAErD,YAAAF,aAAO,EAAE,CAAC,MAAM,CAAC,GAAG,YAAA;;AAElB,gBAAA,IAAI,CAAC,CAAC,IAAI,CAAC,IAAI,EAAE;AACf,oBAAA,cAAc,EAAE,CAAC;AAClB,iBAAA;AAAM,qBAAA;;AAEL,oBAAA,MAAM,CAACX,kBAAY,CAAC,IAAI,EAAA,wBAAA,4CAAuC,CAAC,CAAC;AAClE,iBAAA;AACH,aAAC,CAAC;;AAEF,YAAA,OAAOc,aACG,CAAC,UAAGC,oBAAiB,EAAE,EAAA,UAAA,CAAA,CAAA,MAAA,CAAW,MAAM,CAAE,CAAC;AAClD,iBAAA,KAAK,CAAC,UAAA,CAAC,EAAA,EAAI,OAAA,MAAM,CAAC,CAAC,CAAC,CAAA,EAAA,CAAC,CAAC;AAC1B,SAAA;AACH,KAAC,CAAC,CAAC,KAAK,CAAC,UAAA,KAAK,EAAA;;QAEZ,gBAAgB,GAAG,IAAI,CAAC;AACxB,QAAA,MAAM,KAAK,CAAC;AACd,KAAC,CAAC,CAAC;AACL,CAAC;AAED,IAAI,gBAAgB,GAAyC,IAAI,CAAC;AAC5D,SAAU,SAAS,CAAC,IAAkB,EAAA;AAC1C,IAAA,gBAAgB,GAAG,gBAAgB,IAAI,QAAQ,CAAC,IAAI,CAAC,CAAC;AACtD,IAAA,OAAO,gBAAgB,CAAC;AAC1B;;ACxHA;;;;;;;;;;;;;;;AAeG;AAcH,IAAM,YAAY,GAAG,IAAIjB,WAAK,CAAC,IAAI,EAAE,KAAK,CAAC,CAAC;AAC5C,IAAM,WAAW,GAAG,gBAAgB,CAAC;AACrC,IAAM,oBAAoB,GAAG,sBAAsB,CAAC;AAEpD,IAAM,iBAAiB,GAAG;AACxB,IAAA,KAAK,EAAE;AACL,QAAA,QAAQ,EAAE,UAAU;AACpB,QAAA,GAAG,EAAE,QAAQ;AACb,QAAA,KAAK,EAAE,KAAK;AACZ,QAAA,MAAM,EAAE,KAAK;AACd,KAAA;AACD,IAAA,aAAa,EAAE,MAAM;AACrB,IAAA,QAAQ,EAAE,IAAI;CACf,CAAC;AAEF;AACA;AACA,IAAM,gBAAgB,GAAG,IAAI,GAAG,CAAC;AAC/B,IAAA,CAAA,gCAAA,+BAAyB,GAAG,CAAC;IAC7B,CAAC,gDAAgD,EAAE,GAAG,CAAC;AACvD,IAAA,CAAC,6CAA6C,EAAE,GAAG,CAAC;AACrD,CAAA,CAAC,CAAC;AAEH,SAAS,YAAY,CAAC,IAAkB,EAAA;AACtC,IAAA,IAAM,MAAM,GAAG,IAAI,CAAC,MAAM,CAAC;AAC3B,IAAAR,aAAO,CAAC,MAAM,CAAC,UAAU,EAAE,IAAI,wEAAoC,CAAC;AACpE,IAAA,IAAM,GAAG,GAAG,MAAM,CAAC,QAAQ;AACzB,UAAE0B,kBAAY,CAAC,MAAM,EAAE,oBAAoB,CAAC;UAC1C,UAAW,CAAA,MAAA,CAAA,IAAI,CAAC,MAAM,CAAC,UAAU,EAAA,GAAA,CAAA,CAAA,MAAA,CAAI,WAAW,CAAE,CAAC;AAEvD,IAAA,IAAM,MAAM,GAA2B;QACrC,MAAM,EAAE,MAAM,CAAC,MAAM;QACrB,OAAO,EAAE,IAAI,CAAC,IAAI;AAClB,QAAA,CAAC,EAAEC,eAAW;KACf,CAAC;AACF,IAAA,IAAM,GAAG,GAAG,gBAAgB,CAAC,GAAG,CAAC,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC;AACtD,IAAA,IAAI,GAAG,EAAE;AACP,QAAA,MAAM,CAAC,GAAG,GAAG,GAAG,CAAC;AAClB,KAAA;AACD,IAAA,IAAM,UAAU,GAAG,IAAI,CAAC,cAAc,EAAE,CAAC;IACzC,IAAI,UAAU,CAAC,MAAM,EAAE;QACrB,MAAM,CAAC,EAAE,GAAG,UAAU,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;AAClC,KAAA;AACD,IAAA,OAAO,EAAG,CAAA,MAAA,CAAA,GAAG,EAAI,GAAA,CAAA,CAAA,MAAA,CAAAC,gBAAW,CAAC,MAAM,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,CAAE,CAAC;AAClD,CAAC;AAEK,SAAgB,WAAW,CAC/B,IAAkB,EAAA;;;;;;AAEF,gBAAA,KAAA,CAAA,EAAA,OAAA,CAAA,CAAA,YAAMC,SAAoB,CAAC,IAAI,CAAC,CAAA,CAAA;;AAA1C,oBAAA,OAAO,GAAG,EAAgC,CAAA,IAAA,EAAA,CAAA;AAC1C,oBAAA,IAAI,GAAGR,aAAO,EAAE,CAAC,IAAI,CAAC;AAC5B,oBAAArB,aAAO,CAAC,IAAI,EAAE,IAAI,sDAA+B,CAAC;oBAClD,OAAO,CAAA,CAAA,aAAA,OAAO,CAAC,IAAI,CACjB;4BACE,KAAK,EAAE,QAAQ,CAAC,IAAI;AACpB,4BAAA,GAAG,EAAE,YAAY,CAAC,IAAI,CAAC;AACvB,4BAAA,qBAAqB,EAAE,IAAI,CAAC,OAAO,CAAC,2BAA2B;AAC/D,4BAAA,UAAU,EAAE,iBAAiB;AAC7B,4BAAA,SAAS,EAAE,IAAI;AAChB,yBAAA,EACD,UAAC,MAA2B,EAAA;AAC1B,4BAAA,OAAA,IAAI,OAAO,CAAC,UAAO,OAAO,EAAE,MAAM,EAAA,EAAA,OAAAP,eAAA,CAAA,KAAA,EAAA,KAAA,CAAA,EAAA,KAAA,CAAA,EAAA,YAAA;;AAgBhC,gCAAA,SAAS,oBAAoB,GAAA;AAC3B,oCAAA4B,aAAO,EAAE,CAAC,YAAY,CAAC,iBAAiB,CAAC,CAAC;oCAC1C,OAAO,CAAC,MAAM,CAAC,CAAC;iCACjB;;;;gDAlBD,OAAM,CAAA,CAAA,YAAA,MAAM,CAAC,OAAO,CAAC;;AAEnB,gDAAA,cAAc,EAAE,KAAK;AACtB,6CAAA,CAAC,CAAA,CAAA;;AAHF,4CAAA,EAAA,CAAA,IAAA,EAGE,CAAC;AAEG,4CAAA,YAAY,GAAGX,kBAAY,CAC/B,IAAI,sEAEL,CAAC;AAGI,4CAAA,iBAAiB,GAAGW,aAAO,EAAE,CAAC,UAAU,CAAC,YAAA;gDAC7C,MAAM,CAAC,YAAY,CAAC,CAAC;AACvB,6CAAC,EAAE,YAAY,CAAC,GAAG,EAAE,CAAC,CAAC;;;4CAQvB,MAAM,CAAC,IAAI,CAAC,oBAAoB,CAAC,CAAC,IAAI,CAAC,oBAAoB,EAAE,YAAA;gDAC3D,MAAM,CAAC,YAAY,CAAC,CAAC;AACvB,6CAAC,CAAC,CAAC;;;;iCACJ,CAAC,CAAA;AAzBF,yBAyBE,CACL,CAAC,CAAA;;;;AACH;;ACrHD;;;;;;;;;;;;;;;AAeG;AAaH,IAAM,kBAAkB,GAAG;AACzB,IAAA,QAAQ,EAAE,KAAK;AACf,IAAA,SAAS,EAAE,KAAK;AAChB,IAAA,SAAS,EAAE,KAAK;AAChB,IAAA,OAAO,EAAE,IAAI;CACd,CAAC;AAEF,IAAM,aAAa,GAAG,GAAG,CAAC;AAC1B,IAAM,cAAc,GAAG,GAAG,CAAC;AAC3B,IAAM,YAAY,GAAG,QAAQ,CAAC;AAE9B,IAAM,iBAAiB,GAAG,kBAAkB,CAAC;AAE7C,IAAA,SAAA,kBAAA,YAAA;AAGE,IAAA,SAAA,SAAA,CAAqB,MAAqB,EAAA;QAArB,IAAM,CAAA,MAAA,GAAN,MAAM,CAAe;QAF1C,IAAe,CAAA,eAAA,GAAkB,IAAI,CAAC;KAEQ;AAE9C,IAAA,SAAA,CAAA,SAAA,CAAA,KAAK,GAAL,YAAA;QACE,IAAI,IAAI,CAAC,MAAM,EAAE;YACf,IAAI;AACF,gBAAA,IAAI,CAAC,MAAM,CAAC,KAAK,EAAE,CAAC;AACrB,aAAA;YAAC,OAAO,CAAC,EAAE,GAAE;AACf,SAAA;KACF,CAAA;IACH,OAAC,SAAA,CAAA;AAAD,CAAC,EAAA,EAAA;AAEK,SAAU,KAAK,CACnB,IAAkB,EAClB,GAAY,EACZ,IAAa,EACb,KAAqB,EACrB,MAAuB,EAAA;AADvB,IAAA,IAAA,KAAA,KAAA,KAAA,CAAA,EAAA,EAAA,KAAqB,GAAA,aAAA,CAAA,EAAA;AACrB,IAAA,IAAA,MAAA,KAAA,KAAA,CAAA,EAAA,EAAA,MAAuB,GAAA,cAAA,CAAA,EAAA;IAEvB,IAAM,GAAG,GAAG,IAAI,CAAC,GAAG,CAAC,CAAC,MAAM,CAAC,MAAM,CAAC,WAAW,GAAG,MAAM,IAAI,CAAC,EAAE,CAAC,CAAC,CAAC,QAAQ,EAAE,CAAC;IAC7E,IAAM,IAAI,GAAG,IAAI,CAAC,GAAG,CAAC,CAAC,MAAM,CAAC,MAAM,CAAC,UAAU,GAAG,KAAK,IAAI,CAAC,EAAE,CAAC,CAAC,CAAC,QAAQ,EAAE,CAAC;IAC5E,IAAI,MAAM,GAAG,EAAE,CAAC;IAEhB,IAAM,OAAO,GACRS,cAAA,CAAAA,cAAA,CAAA,EAAA,EAAA,kBAAkB,CACrB,EAAA,EAAA,KAAK,EAAE,KAAK,CAAC,QAAQ,EAAE,EACvB,MAAM,EAAE,MAAM,CAAC,QAAQ,EAAE,EACzB,GAAG,KAAA,EACH,IAAI,EAAA,IAAA,EAAA,CACL,CAAC;;;AAIF,IAAA,IAAM,EAAE,GAAGC,UAAK,EAAE,CAAC,WAAW,EAAE,CAAC;AAEjC,IAAA,IAAI,IAAI,EAAE;AACR,QAAA,MAAM,GAAGC,kBAAY,CAAC,EAAE,CAAC,GAAG,YAAY,GAAG,IAAI,CAAC;AACjD,KAAA;AAED,IAAA,IAAIC,gBAAU,CAAC,EAAE,CAAC,EAAE;;AAElB,QAAA,GAAG,GAAG,GAAG,IAAI,iBAAiB,CAAC;;;AAG/B,QAAA,OAAO,CAAC,UAAU,GAAG,KAAK,CAAC;AAC5B,KAAA;AAED,IAAA,IAAM,aAAa,GAAG,MAAM,CAAC,OAAO,CAAC,OAAO,CAAC,CAAC,MAAM,CAClD,UAAC,KAAK,EAAE,EAAY,EAAA;YAAX,GAAG,GAAA,EAAA,CAAA,CAAA,CAAA,EAAE,KAAK,GAAA,EAAA,CAAA,CAAA,CAAA,CAAA;AAAM,QAAA,OAAA,UAAG,KAAK,CAAA,CAAA,MAAA,CAAG,GAAG,EAAA,GAAA,CAAA,CAAA,MAAA,CAAI,KAAK,EAAG,GAAA,CAAA,CAAA;KAAA,EACnD,EAAE,CACH,CAAC;IAEF,IAAIC,sBAAgB,CAAC,EAAE,CAAC,IAAI,MAAM,KAAK,OAAO,EAAE;AAC9C,QAAA,kBAAkB,CAAC,GAAG,IAAI,EAAE,EAAE,MAAM,CAAC,CAAC;AACtC,QAAA,OAAO,IAAI,SAAS,CAAC,IAAI,CAAC,CAAC;AAC5B,KAAA;;;AAID,IAAA,IAAM,MAAM,GAAG,MAAM,CAAC,IAAI,CAAC,GAAG,IAAI,EAAE,EAAE,MAAM,EAAE,aAAa,CAAC,CAAC;AAC7D,IAAAlC,aAAO,CAAC,MAAM,EAAE,IAAI,oDAA8B,CAAC;;IAGnD,IAAI;QACF,MAAM,CAAC,KAAK,EAAE,CAAC;AAChB,KAAA;IAAC,OAAO,CAAC,EAAE,GAAE;AAEd,IAAA,OAAO,IAAI,SAAS,CAAC,MAAM,CAAC,CAAC;AAC/B,CAAC;AAED,SAAS,kBAAkB,CAAC,GAAW,EAAE,MAAc,EAAA;IACrD,IAAM,EAAE,GAAG,QAAQ,CAAC,aAAa,CAAC,GAAG,CAAC,CAAC;AACvC,IAAA,EAAE,CAAC,IAAI,GAAG,GAAG,CAAC;AACd,IAAA,EAAE,CAAC,MAAM,GAAG,MAAM,CAAC;IACnB,IAAM,KAAK,GAAG,QAAQ,CAAC,WAAW,CAAC,YAAY,CAAC,CAAC;AACjD,IAAA,KAAK,CAAC,cAAc,CAClB,OAAO,EACP,IAAI,EACJ,IAAI,EACJ,MAAM,EACN,CAAC,EACD,CAAC,EACD,CAAC,EACD,CAAC,EACD,CAAC,EACD,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,CAAC,EACD,IAAI,CACL,CAAC;AACF,IAAA,EAAE,CAAC,aAAa,CAAC,KAAK,CAAC,CAAC;AAC1B;;ACxIA;;;;;;;;;;;;;;;AAeG;AAaH;;;;AAIG;AACH,IAAM,WAAW,GAAG,iBAAiB,CAAC;AAEtC;;;;AAIG;AACH,IAAM,oBAAoB,GAAG,uBAAuB,CAAC;AAErD;;;;AAIG;AACH,IAAM,8BAA8B,GAAG,kBAAkB,CAAC,KAAK,CAAC,CAAC;AAgB3C,SAAA,eAAe,CACnC,IAAkB,EAClB,QAAsB,EACtB,QAAuB,EACvB,WAAoB,EACpB,OAAgB,EAChB,gBAAyC,EAAA;;;;;;oBAEzCA,aAAO,CAAC,IAAI,CAAC,MAAM,CAAC,UAAU,EAAE,IAAI,EAAA,6BAAA,yCAAoC,CAAC;oBACzEA,aAAO,CAAC,IAAI,CAAC,MAAM,CAAC,MAAM,EAAE,IAAI,EAAA,iBAAA,qCAAgC,CAAC;AAE3D,oBAAA,MAAM,GAAiB;AAC3B,wBAAA,MAAM,EAAE,IAAI,CAAC,MAAM,CAAC,MAAM;wBAC1B,OAAO,EAAE,IAAI,CAAC,IAAI;AAClB,wBAAA,QAAQ,EAAA,QAAA;AACR,wBAAA,WAAW,EAAA,WAAA;AACX,wBAAA,CAAC,EAAE2B,eAAW;AACd,wBAAA,OAAO,EAAA,OAAA;qBACR,CAAC;oBAEF,IAAI,QAAQ,YAAYd,2BAAqB,EAAE;AAC7C,wBAAA,QAAQ,CAAC,kBAAkB,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC;wBAC/C,MAAM,CAAC,UAAU,GAAG,QAAQ,CAAC,UAAU,IAAI,EAAE,CAAC;wBAC9C,IAAI,CAACsB,YAAO,CAAC,QAAQ,CAAC,mBAAmB,EAAE,CAAC,EAAE;AAC5C,4BAAA,MAAM,CAAC,gBAAgB,GAAG,IAAI,CAAC,SAAS,CAAC,QAAQ,CAAC,mBAAmB,EAAE,CAAC,CAAC;AAC1E,yBAAA;;AAGD,wBAAA,KAAA,EAAA,GAAA,CAAiE,EAAtC,EAAA,GAAA,MAAM,CAAC,OAAO,CAAC,gBAAgB,IAAI,EAAE,CAAC,EAAtC,EAAsC,GAAA,EAAA,CAAA,MAAA,EAAtC,IAAsC,EAAE;AAAxD,4BAAA,EAAA,GAAA,EAAA,CAAA,EAAA,CAAY,EAAX,GAAG,GAAA,EAAA,CAAA,CAAA,CAAA,EAAE,KAAK,GAAA,EAAA,CAAA,CAAA,CAAA,CAAA;AACpB,4BAAA,MAAM,CAAC,GAAG,CAAC,GAAG,KAAK,CAAC;AACrB,yBAAA;AACF,qBAAA;oBAED,IAAI,QAAQ,YAAYC,uBAAiB,EAAE;AACnC,wBAAA,MAAM,GAAG,QAAQ,CAAC,SAAS,EAAE,CAAC,MAAM,CAAC,UAAA,KAAK,EAAA,EAAI,OAAA,KAAK,KAAK,EAAE,CAAZ,EAAY,CAAC,CAAC;AAClE,wBAAA,IAAI,MAAM,CAAC,MAAM,GAAG,CAAC,EAAE;4BACrB,MAAM,CAAC,MAAM,GAAG,MAAM,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;AAClC,yBAAA;AACF,qBAAA;oBAED,IAAI,IAAI,CAAC,QAAQ,EAAE;AACjB,wBAAA,MAAM,CAAC,GAAG,GAAG,IAAI,CAAC,QAAQ,CAAC;AAC5B,qBAAA;oBAKK,UAAU,GAAG,MAAyC,CAAC;AAC7D,oBAAA,KAAA,EAAA,GAAA,CAAyC,EAAvB,EAAA,GAAA,MAAM,CAAC,IAAI,CAAC,UAAU,CAAC,EAAvB,EAAA,GAAA,EAAA,CAAA,MAAuB,EAAvB,EAAA,EAAuB,EAAE;wBAAhC,GAAG,GAAA,EAAA,CAAA,EAAA,CAAA,CAAA;AACZ,wBAAA,IAAI,UAAU,CAAC,GAAG,CAAC,KAAK,SAAS,EAAE;AACjC,4BAAA,OAAO,UAAU,CAAC,GAAG,CAAC,CAAC;AACxB,yBAAA;AACF,qBAAA;AAGqB,oBAAA,OAAA,CAAA,CAAA,YAAM,IAAI,CAAC,iBAAiB,EAAE,CAAA,CAAA;;AAA9C,oBAAA,aAAa,GAAG,EAA8B,CAAA,IAAA,EAAA,CAAA;AAC9C,oBAAA,qBAAqB,GAAG,aAAa;0BACvC,WAAI,8BAA8B,EAAA,GAAA,CAAA,CAAA,MAAA,CAAI,kBAAkB,CAAC,aAAa,CAAC,CAAE;0BACzE,EAAE,CAAC;;AAGP,oBAAA,OAAA,CAAA,CAAA,aAAO,UAAG,cAAc,CAAC,IAAI,CAAC,EAAA,GAAA,CAAA,CAAA,MAAA,CAAIR,gBAAW,CAAC,UAAU,CAAC,CAAC,KAAK,CAC7D,CAAC,CACF,CAAG,CAAA,MAAA,CAAA,qBAAqB,CAAE,CAAC,CAAA;;;;AAC7B,CAAA;AAED,SAAS,cAAc,CAAC,EAAwB,EAAA;AAAtB,IAAA,IAAA,MAAM,GAAA,EAAA,CAAA,MAAA,CAAA;AAC9B,IAAA,IAAI,CAAC,MAAM,CAAC,QAAQ,EAAE;AACpB,QAAA,OAAO,kBAAW,MAAM,CAAC,UAAU,EAAI,GAAA,CAAA,CAAA,MAAA,CAAA,WAAW,CAAE,CAAC;AACtD,KAAA;AAED,IAAA,OAAOF,kBAAY,CAAC,MAAM,EAAE,oBAAoB,CAAC,CAAC;AACpD;;ACvIA;;;;;;;;;;;;;;;AAeG;AA2BH;;;AAGG;AACH,IAAM,uBAAuB,GAAG,mBAAmB,CAAC;AAWpD,IAAA,4BAAA,kBAAA,YAAA;AAAA,IAAA,SAAA,4BAAA,GAAA;QACmB,IAAa,CAAA,aAAA,GAAqC,EAAE,CAAC;QACrD,IAAO,CAAA,OAAA,GAAwC,EAAE,CAAC;QAClD,IAAwB,CAAA,wBAAA,GAAkC,EAAE,CAAC;QAErE,IAAoB,CAAA,oBAAA,GAAG,yBAAyB,CAAC;QAyH1D,IAAmB,CAAA,mBAAA,GAAG,kBAAkB,CAAC;QAEzC,IAAuB,CAAA,uBAAA,GAAG,uBAAuB,CAAC;KACnD;;;IAxHO,4BAAU,CAAA,SAAA,CAAA,UAAA,GAAhB,UACE,IAAkB,EAClB,QAAsB,EACtB,QAAuB,EACvB,OAAgB,EAAA;;;;;;;AAEhB,wBAAAnB,iBAAW,CACT,CAAA,EAAA,GAAA,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,IAAI,EAAE,CAAC,MAAE,IAAA,IAAA,EAAA,KAAA,KAAA,CAAA,GAAA,KAAA,CAAA,GAAA,EAAA,CAAA,OAAO,EACxC,8CAA8C,CAC/C,CAAC;AAEU,wBAAA,OAAA,CAAA,CAAA,YAAM,eAAe,CAC/B,IAAI,EACJ,QAAQ,EACR,QAAQ,EACRa,oBAAc,EAAE,EAChB,OAAO,CACR,CAAA,CAAA;;AANK,wBAAA,GAAG,GAAG,EAMX,CAAA,IAAA,EAAA,CAAA;wBACD,OAAO,CAAA,CAAA,aAAA,KAAK,CAAC,IAAI,EAAE,GAAG,EAAE,gBAAgB,EAAE,CAAC,CAAC,CAAA;;;;AAC7C,KAAA,CAAA;IAEK,4BAAa,CAAA,SAAA,CAAA,aAAA,GAAnB,UACE,IAAkB,EAClB,QAAsB,EACtB,QAAuB,EACvB,OAAgB,EAAA;;;;;AAEhB,oBAAA,KAAA,CAAA,EAAA,OAAA,CAAA,CAAA,YAAM,IAAI,CAAC,iBAAiB,CAAC,IAAI,CAAC,CAAA,CAAA;;AAAlC,wBAAA,EAAA,CAAA,IAAA,EAAkC,CAAC;AACvB,wBAAA,OAAA,CAAA,CAAA,YAAM,eAAe,CAC/B,IAAI,EACJ,QAAQ,EACR,QAAQ,EACRA,oBAAc,EAAE,EAChB,OAAO,CACR,CAAA,CAAA;;AANK,wBAAA,GAAG,GAAG,EAMX,CAAA,IAAA,EAAA,CAAA;wBACDiB,wBAAkB,CAAC,GAAG,CAAC,CAAC;AACxB,wBAAA,OAAA,CAAA,CAAA,aAAO,IAAI,OAAO,CAAC,YAAO,GAAC,CAAC,CAAC,CAAA;;;;AAC9B,KAAA,CAAA;IAED,4BAAW,CAAA,SAAA,CAAA,WAAA,GAAX,UAAY,IAAkB,EAAA;QAA9B,IAsBC,KAAA,GAAA,IAAA,CAAA;AArBC,QAAA,IAAM,GAAG,GAAG,IAAI,CAAC,IAAI,EAAE,CAAC;AACxB,QAAA,IAAI,IAAI,CAAC,aAAa,CAAC,GAAG,CAAC,EAAE;AACrB,YAAA,IAAA,EAAuB,GAAA,IAAI,CAAC,aAAa,CAAC,GAAG,CAAC,EAA5C,OAAO,GAAA,EAAA,CAAA,OAAA,EAAE,SAAO,aAA4B,CAAC;AACrD,YAAA,IAAI,OAAO,EAAE;AACX,gBAAA,OAAO,OAAO,CAAC,OAAO,CAAC,OAAO,CAAC,CAAC;AACjC,aAAA;AAAM,iBAAA;AACL,gBAAA9B,iBAAW,CAAC,SAAO,EAAE,0CAA0C,CAAC,CAAC;AACjE,gBAAA,OAAO,SAAO,CAAC;AAChB,aAAA;AACF,SAAA;QAED,IAAM,OAAO,GAAG,IAAI,CAAC,iBAAiB,CAAC,IAAI,CAAC,CAAC;QAC7C,IAAI,CAAC,aAAa,CAAC,GAAG,CAAC,GAAG,EAAE,OAAO,EAAA,OAAA,EAAE,CAAC;;;QAItC,OAAO,CAAC,KAAK,CAAC,YAAA;AACZ,YAAA,OAAO,KAAI,CAAC,aAAa,CAAC,GAAG,CAAC,CAAC;AACjC,SAAC,CAAC,CAAC;AAEH,QAAA,OAAO,OAAO,CAAC;KAChB,CAAA;IAEa,4BAAiB,CAAA,SAAA,CAAA,iBAAA,GAA/B,UAAgC,IAAkB,EAAA;;;;;AACjC,oBAAA,KAAA,CAAA,EAAA,OAAA,CAAA,CAAA,YAAM,WAAW,CAAC,IAAI,CAAC,CAAA,CAAA;;AAAhC,wBAAA,MAAM,GAAG,EAAuB,CAAA,IAAA,EAAA,CAAA;AAChC,wBAAA,OAAO,GAAG,IAAI,gBAAgB,CAAC,IAAI,CAAC,CAAC;AAC3C,wBAAA,MAAM,CAAC,QAAQ,CACb,WAAW,EACX,UAAC,WAAiC,EAAA;4BAChCP,aAAO,CAAC,WAAW,KAAA,IAAA,IAAX,WAAW,KAAA,KAAA,CAAA,GAAA,KAAA,CAAA,GAAX,WAAW,CAAE,SAAS,EAAE,IAAI,EAAA,oBAAA,wCAAmC,CAAC;;4BAGxE,IAAM,OAAO,GAAG,OAAO,CAAC,OAAO,CAAC,WAAW,CAAC,SAAS,CAAC,CAAC;4BACvD,OAAO,EAAE,MAAM,EAAE,OAAO,GAAmB,KAAA,yBAAmB,OAAA,0BAAE,CAAC;AACnE,yBAAC,EACD,IAAI,CAAC,OAAO,CAAC,2BAA2B,CACzC,CAAC;AAEF,wBAAA,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,IAAI,EAAE,CAAC,GAAG,EAAE,OAAO,EAAA,OAAA,EAAE,CAAC;wBAC9C,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,IAAI,EAAE,CAAC,GAAG,MAAM,CAAC;AACnC,wBAAA,OAAA,CAAA,CAAA,aAAO,OAAO,CAAC,CAAA;;;;AAChB,KAAA,CAAA;AAED,IAAA,4BAAA,CAAA,SAAA,CAAA,4BAA4B,GAA5B,UACE,IAAkB,EAClB,EAAmC,EAAA;QAEnC,IAAM,MAAM,GAAG,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,IAAI,EAAE,CAAC,CAAC;AACzC,QAAA,MAAM,CAAC,IAAI,CACT,uBAAuB,EACvB,EAAE,IAAI,EAAE,uBAAuB,EAAE,EACjC,UAAA,MAAM,EAAA;;AACJ,YAAA,IAAM,WAAW,GAAG,CAAA,EAAA,GAAA,MAAM,aAAN,MAAM,KAAA,KAAA,CAAA,GAAA,KAAA,CAAA,GAAN,MAAM,CAAG,CAAC,CAAC,MAAG,IAAA,IAAA,EAAA,KAAA,KAAA,CAAA,GAAA,KAAA,CAAA,GAAA,EAAA,CAAA,uBAAuB,CAAC,CAAC;YAC3D,IAAI,WAAW,KAAK,SAAS,EAAE;AAC7B,gBAAA,EAAE,CAAC,CAAC,CAAC,WAAW,CAAC,CAAC;AACnB,aAAA;YAEDM,WAAK,CAAC,IAAI,EAAA,gBAAA,oCAA+B,CAAC;AAC5C,SAAC,EACD,IAAI,CAAC,OAAO,CAAC,2BAA2B,CACzC,CAAC;KACH,CAAA;IAED,4BAAiB,CAAA,SAAA,CAAA,iBAAA,GAAjB,UAAkB,IAAkB,EAAA;AAClC,QAAA,IAAM,GAAG,GAAG,IAAI,CAAC,IAAI,EAAE,CAAC;AACxB,QAAA,IAAI,CAAC,IAAI,CAAC,wBAAwB,CAAC,GAAG,CAAC,EAAE;YACvC,IAAI,CAAC,wBAAwB,CAAC,GAAG,CAAC,GAAGa,iBAAe,CAAC,IAAI,CAAC,CAAC;AAC5D,SAAA;AAED,QAAA,OAAO,IAAI,CAAC,wBAAwB,CAAC,GAAG,CAAC,CAAC;KAC3C,CAAA;AAED,IAAA,MAAA,CAAA,cAAA,CAAI,4BAAsB,CAAA,SAAA,EAAA,wBAAA,EAAA;AAA1B,QAAA,GAAA,EAAA,YAAA;;YAEE,OAAO5B,sBAAgB,EAAE,IAAI+C,eAAS,EAAE,IAAIC,YAAM,EAAE,CAAC;SACtD;;;AAAA,KAAA,CAAA,CAAA;IAKH,OAAC,4BAAA,CAAA;AAAD,CAAC,EAAA,CAAA,CAAA;AAED;;;;;;;;AAQG;AACI,IAAM,4BAA4B,GACvC;;ACtMF;;;;;;;;;;;;;;;AAeG;AAsBH,IAAM,wBAAwB,GAAG,CAAC,GAAG,EAAE,CAAC;AACxC,IAAM,iBAAiB,GACrBC,2BAAsB,CAAC,mBAAmB,CAAC,IAAI,wBAAwB,CAAC;AAE1E,IAAI,iBAAiB,GAA8B,IAAI,CAAC;AAExD,IAAM,iBAAiB,GAAG,UAAC,GAAW,EAAK,EAAA,OAAA,UAAO,IAAiB,EAAA,EAAA,OAAA/C,eAAA,CAAA,KAAA,CAAA,EAAA,KAAA,CAAA,EAAA,KAAA,CAAA,EAAA,YAAA;;;;;AAC3C,gBAAA,EAAA,GAAA,IAAI,CAAA;yBAAJ,OAAI,CAAA,CAAA,YAAA,CAAA,CAAA,CAAA;AAAK,gBAAA,OAAA,CAAA,CAAA,YAAM,IAAI,CAAC,gBAAgB,EAAE,CAAA,CAAA;;gBAA9B,EAAA,IAAC,EAA6B,CAAA,IAAA,EAAA,CAAC,CAAA;;;AAAvD,gBAAA,aAAa,GAA0C,EAAA,CAAA;AACvD,gBAAA,UAAU,GACd,aAAa;AACb,oBAAA,CAAC,IAAI,IAAI,EAAE,CAAC,OAAO,EAAE,GAAG,IAAI,CAAC,KAAK,CAAC,aAAa,CAAC,YAAY,CAAC,IAAI,IAAK,CAAC;AAC1E,gBAAA,IAAI,UAAU,IAAI,UAAU,GAAG,iBAAiB,EAAE;oBAChD,OAAO,CAAA,CAAA,YAAA,CAAA;AACR,iBAAA;gBAEK,OAAO,GAAG,aAAa,KAAb,IAAA,IAAA,aAAa,uBAAb,aAAa,CAAE,KAAK,CAAC;gBACrC,IAAI,iBAAiB,KAAK,OAAO,EAAE;oBACjC,OAAO,CAAA,CAAA,YAAA,CAAA;AACR,iBAAA;gBACD,iBAAiB,GAAG,OAAO,CAAC;gBAC5B,OAAM,CAAA,CAAA,YAAA,KAAK,CAAC,GAAG,EAAE;wBACf,MAAM,EAAE,OAAO,GAAG,MAAM,GAAG,QAAQ;AACnC,wBAAA,OAAO,EAAE,OAAO;AACd,8BAAE;gCACE,eAAe,EAAE,SAAU,CAAA,MAAA,CAAA,OAAO,CAAE;AACrC,6BAAA;AACH,8BAAE,EAAE;AACP,qBAAA,CAAC,CAAA,CAAA;;AAPF,gBAAA,EAAA,CAAA,IAAA,EAOE,CAAC;;;;AACJ,CAAA,CAAA,CAAA,EAAA,CAAA,EAAA,CAAC;AAEF;;;;;;;AAOG;AACG,SAAU,OAAO,CAACgD,KAA2B,EAAA;IAA3B,IAAAA,KAAA,KAAA,KAAA,CAAA,EAAA,EAAAA,KAAmB,GAAAC,UAAM,EAAE,CAAA,EAAA;IACjD,IAAM,QAAQ,GAAGC,gBAAY,CAACF,KAAG,EAAE,MAAM,CAAC,CAAC;AAE3C,IAAA,IAAI,QAAQ,CAAC,aAAa,EAAE,EAAE;AAC5B,QAAA,OAAO,QAAQ,CAAC,YAAY,EAAE,CAAC;AAChC,KAAA;AAED,IAAA,IAAM,IAAI,GAAGG,oBAAc,CAACH,KAAG,EAAE;AAC/B,QAAA,qBAAqB,EAAE,4BAA4B;AACnD,QAAA,WAAW,EAAE;YACX,yBAAyB;YACzB,uBAAuB;YACvB,yBAAyB;AAC1B,SAAA;AACF,KAAA,CAAC,CAAC;AAEH,IAAA,IAAM,iBAAiB,GAAGD,2BAAsB,CAAC,kBAAkB,CAAC,CAAC;;AAErE,IAAA,IACE,iBAAiB;QACjB,OAAO,eAAe,KAAK,SAAS;AACpC,QAAA,eAAe,EACf;;QAEA,IAAM,gBAAgB,GAAG,IAAI,GAAG,CAAC,iBAAiB,EAAE,QAAQ,CAAC,MAAM,CAAC,CAAC;AACrE,QAAA,IAAI,QAAQ,CAAC,MAAM,KAAK,gBAAgB,CAAC,MAAM,EAAE;YAC/C,IAAM,YAAU,GAAG,iBAAiB,CAAC,gBAAgB,CAAC,QAAQ,EAAE,CAAC,CAAC;AAClE,YAAAK,4BAAsB,CAAC,IAAI,EAAE,YAAU,EAAE,YAAA;AACvC,gBAAA,OAAA,YAAU,CAAC,IAAI,CAAC,WAAW,CAAC,CAAA;AAA5B,aAA4B,CAC7B,CAAC;AACF,YAAAC,sBAAgB,CAAC,IAAI,EAAE,UAAA,IAAI,EAAI,EAAA,OAAA,YAAU,CAAC,IAAI,CAAC,CAAhB,EAAgB,CAAC,CAAC;AAClD,SAAA;AACF,KAAA;AAED,IAAA,IAAM,gBAAgB,GAAGC,2BAAsB,CAAC,MAAM,CAAC,CAAC;AACxD,IAAA,IAAI,gBAAgB,EAAE;AACpB,QAAAC,yBAAmB,CAAC,IAAI,EAAE,iBAAU,gBAAgB,CAAE,CAAC,CAAC;AACzD,KAAA;AAED,IAAA,OAAO,IAAI,CAAC;AACd,CAAC;AAED,SAAS,sBAAsB,GAAA;;AAC7B,IAAA,OAAO,CAAA,EAAA,GAAA,CAAA,EAAA,GAAA,QAAQ,CAAC,oBAAoB,CAAC,MAAM,CAAC,MAAG,IAAA,IAAA,EAAA,KAAA,KAAA,CAAA,GAAA,KAAA,CAAA,GAAA,EAAA,CAAA,CAAC,CAAC,MAAA,IAAA,IAAA,EAAA,KAAA,KAAA,CAAA,GAAA,EAAA,GAAI,QAAQ,CAAC;AAChE,CAAC;AAEDC,4BAAsB,CAAC;IACrB,MAAM,EAAN,UAAO,GAAW,EAAA;;AAEhB,QAAA,OAAO,IAAI,OAAO,CAAC,UAAC,OAAO,EAAE,MAAM,EAAA;YACjC,IAAM,EAAE,GAAG,QAAQ,CAAC,aAAa,CAAC,QAAQ,CAAC,CAAC;AAC5C,YAAA,EAAE,CAAC,YAAY,CAAC,KAAK,EAAE,GAAG,CAAC,CAAC;AAC5B,YAAA,EAAE,CAAC,MAAM,GAAG,OAAO,CAAC;AACpB,YAAA,EAAE,CAAC,OAAO,GAAG,UAAA,CAAC,EAAA;AACZ,gBAAA,IAAM,KAAK,GAAGvC,kBAAY,CAAA,gBAAA,oCAA8B,CAAC;AACzD,gBAAA,KAAK,CAAC,UAAU,GAAG,CAAuC,CAAC;gBAC3D,MAAM,CAAC,KAAK,CAAC,CAAC;AAChB,aAAC,CAAC;AACF,YAAA,EAAE,CAAC,IAAI,GAAG,iBAAiB,CAAC;AAC5B,YAAA,EAAE,CAAC,OAAO,GAAG,OAAO,CAAC;AACrB,YAAA,sBAAsB,EAAE,CAAC,WAAW,CAAC,EAAE,CAAC,CAAC;AAC3C,SAAC,CAAC,CAAC;KACJ;AAED,IAAA,UAAU,EAAE,mCAAmC;AAC/C,IAAA,iBAAiB,EAAE,yCAAyC;AAC5D,IAAA,yBAAyB,EACvB,wDAAwD;AAC3D,CAAA,CAAC,CAAC;AAEHwC,kBAAY,wCAAwB;;ACjJpC;;;;;;;;;;;;;;;AAeG;SAoCa,cAAc,GAAA;AAC5B,IAAA,OAAO,MAAkC,CAAC;AAC5C;;ACrDA;;;;;;;;;;;;;;;AAeG;AAoBH;;;AAGG;AACH,IAAM,mBAAmB,GAAG,IAAI,CAAC;AAEjC;;AAEG;SACmB,mBAAmB,CACvC,IAAkB,EAClB,KAAgB,EAChB,QAAsB,EAAA;;;;;;;AAGd,oBAAA,SAAS,GAAK,cAAc,EAAE,CAAA,SAArB,CAAsB;AACvC,oBAAA3C,iBAAW,CAAC,KAAK,CAAC,SAAS,EAAE,wCAAwC,CAAC,CAAC;AACjD,oBAAA,OAAA,CAAA,CAAA,YAAM,aAAa,CAAC,KAAK,CAAC,SAAS,CAAC,CAAA,CAAA;;AAApD,oBAAA,aAAa,GAAG,EAAoC,CAAA,IAAA,EAAA,CAAA;oBAEpD,gBAAgB,GAA2B,EAAE,CAAC;oBACpD,IAAIgC,YAAM,EAAE,EAAE;;AAEZ,wBAAA,gBAAgB,CAAC,KAAK,CAAC,GAAG,SAAS,CAAC,WAAW,CAAC;AACjD,qBAAA;yBAAM,IAAIY,gBAAU,EAAE,EAAE;;AAEvB,wBAAA,gBAAgB,CAAC,KAAK,CAAC,GAAG,SAAS,CAAC,WAAW,CAAC;AACjD,qBAAA;AAAM,yBAAA;wBACL7C,WAAK,CAAC,IAAI,EAAA,6CAAA,6CAAwC,CAAC;AACpD,qBAAA;;oBAGD,IAAI,SAAS,CAAC,WAAW,EAAE;AACzB,wBAAA,gBAAgB,CAAC,gBAAgB,CAAC,GAAG,SAAS,CAAC,WAAW,CAAC;AAC5D,qBAAA;;AAGD,oBAAA,gBAAgB,CAAC,WAAW,CAAC,GAAG,aAAa,CAAC;oBAC9C,OAAO,CAAA,CAAA,aAAA,eAAe,CACpB,IAAI,EACJ,QAAQ,EACR,KAAK,CAAC,IAAI,EACV,SAAS,EACT,CAAA,EAAA,GAAA,KAAK,CAAC,OAAO,mCAAI,SAAS,EAC1B,gBAAgB,CACjB,CAAC,CAAA;;;;AACH,CAAA;AAED;;AAEG;AACG,SAAgB,eAAe,CAAC,IAAkB,EAAA;;;;;;AAC9C,oBAAA,SAAS,GAAK,cAAc,EAAE,CAAA,SAArB,CAAsB;oBACjC,OAAO,GAA4B,EAAE,CAAC;oBAC5C,IAAIiC,YAAM,EAAE,EAAE;AACZ,wBAAA,OAAO,CAAC,WAAW,GAAG,SAAS,CAAC,WAAW,CAAC;AAC7C,qBAAA;yBAAM,IAAIY,gBAAU,EAAE,EAAE;AACvB,wBAAA,OAAO,CAAC,kBAAkB,GAAG,SAAS,CAAC,WAAW,CAAC;AACpD,qBAAA;AAAM,yBAAA;wBACL7C,WAAK,CAAC,IAAI,EAAA,6CAAA,6CAAwC,CAAC;AACpD,qBAAA;;AAGD,oBAAA,OAAA,CAAA,CAAA,YAAM,iBAAiB,CAAC,IAAI,EAAE,OAAO,CAAC,CAAA,CAAA;;;AAAtC,oBAAA,EAAA,CAAA,IAAA,EAAsC,CAAC;;;;;AACxC,CAAA;AAEK,SAAU,gBAAgB,CAC9B,UAAkB,EAAA;;AAGV,IAAA,IAAA,OAAO,GAAK,cAAc,EAAE,QAArB,CAAsB;AAErC,IAAA,OAAO,IAAI,OAAO,CAAC,UAAA,OAAO,EAAA;QACxB,OAAO,CAAC,OAAO,CAAC,UAAU,CAAC,WAAW,CAAC,UAAA,qBAAqB,EAAA;YAC1D,IAAI,MAAM,GAA2B,IAAI,CAAC;AAC1C,YAAA,IAAI,qBAAqB,EAAE;gBACzB,OAAO,CAAC,OAAO,CAAC,UAAU,CAAC,OAAO,CAAC,UAAU,CAAC,CAAC;AAChD,aAAA;AAAM,iBAAA;;gBAEL,MAAM,GAAG,OAAO,CAAC,YAAY,CAAC,IAAI,CAChC,UAAU,EACV8C,gBAAU,EAAE,GAAG,QAAQ,GAAG,SAAS,EACnC,cAAc,CACf,CAAC;AACH,aAAA;YACD,OAAO,CAAC,MAAM,CAAC,CAAC;AAClB,SAAC,CAAC,CAAC;AACL,KAAC,CAAC,CAAC;AACL,CAAC;AAQD;;;;;AAKG;SACmB,iBAAiB,CACrC,IAAkB,EAClB,aAAuC,EACvC,MAA8B,EAAA;;;;;;AAGtB,oBAAA,OAAO,GAAK,cAAc,EAAE,CAAA,OAArB,CAAsB;oBAEjC,OAAO,GAAG,YAAa,GAAC,CAAC;;;;AAE3B,oBAAA,OAAA,CAAA,CAAA,YAAM,IAAI,OAAO,CAAO,UAAC,OAAO,EAAE,MAAM,EAAA;4BACtC,IAAI,YAAY,GAAkB,IAAI,CAAC;;AAGvC,4BAAA,SAAS,aAAa,GAAA;;;;AAGpB,gCAAA,OAAO,EAAE,CAAC;gCACV,IAAM,eAAe,GAAG,CAAA,EAAA,GAAA,OAAO,CAAC,OAAO,CAAC,UAAU,MAAE,IAAA,IAAA,EAAA,KAAA,KAAA,CAAA,GAAA,KAAA,CAAA,GAAA,EAAA,CAAA,KAAK,CAAC;AAC1D,gCAAA,IAAI,OAAO,eAAe,KAAK,UAAU,EAAE;AACzC,oCAAA,eAAe,EAAE,CAAC;AACnB,iCAAA;;;AAGD,gCAAA,IAAI,QAAO,MAAM,KAAA,IAAA,IAAN,MAAM,KAAA,KAAA,CAAA,GAAA,KAAA,CAAA,GAAN,MAAM,CAAE,KAAK,CAAA,KAAK,UAAU,EAAE;oCACvC,MAAM,CAAC,KAAK,EAAE,CAAC;AAChB,iCAAA;6BACF;AAED,4BAAA,SAAS,OAAO,GAAA;AACd,gCAAA,IAAI,YAAY,EAAE;;oCAEhB,OAAO;AACR,iCAAA;AAED,gCAAA,YAAY,GAAG,MAAM,CAAC,UAAU,CAAC,YAAA;;AAE/B,oCAAA,MAAM,CAAC1C,kBAAY,CAAC,IAAI,EAAA,4BAAA,gDAA2C,CAAC,CAAC;iCACtE,EAAE,mBAAmB,CAAC,CAAC;6BACzB;AAED,4BAAA,SAAS,iBAAiB,GAAA;gCACxB,IAAI,CAAA,QAAQ,KAAA,IAAA,IAAR,QAAQ,KAAA,KAAA,CAAA,GAAA,KAAA,CAAA,GAAR,QAAQ,CAAE,eAAe,MAAK,SAAS,EAAE;AAC3C,oCAAA,OAAO,EAAE,CAAC;AACX,iCAAA;6BACF;;;AAID,4BAAA,aAAa,CAAC,kBAAkB,CAAC,aAAa,CAAC,CAAC;;4BAGhD,QAAQ,CAAC,gBAAgB,CAAC,QAAQ,EAAE,OAAO,EAAE,KAAK,CAAC,CAAC;4BACpD,IAAIyC,gBAAU,EAAE,EAAE;gCAChB,QAAQ,CAAC,gBAAgB,CAAC,kBAAkB,EAAE,iBAAiB,EAAE,KAAK,CAAC,CAAC;AACzE,6BAAA;;AAGD,4BAAA,OAAO,GAAG,YAAA;AACR,gCAAA,aAAa,CAAC,qBAAqB,CAAC,aAAa,CAAC,CAAC;gCACnD,QAAQ,CAAC,mBAAmB,CAAC,QAAQ,EAAE,OAAO,EAAE,KAAK,CAAC,CAAC;gCACvD,QAAQ,CAAC,mBAAmB,CAC1B,kBAAkB,EAClB,iBAAiB,EACjB,KAAK,CACN,CAAC;AACF,gCAAA,IAAI,YAAY,EAAE;AAChB,oCAAA,MAAM,CAAC,YAAY,CAAC,YAAY,CAAC,CAAC;AACnC,iCAAA;AACH,6BAAC,CAAC;AACJ,yBAAC,CAAC,CAAA,CAAA;;AA5DF,oBAAA,EAAA,CAAA,IAAA,EA4DE,CAAC;;;AAEH,oBAAA,OAAO,EAAE,CAAC;;;;;;AAEb,CAAA;AAED;;;;AAIG;AACG,SAAU,0BAA0B,CAAC,IAAkB,EAAA;;AAC3D,IAAA,IAAM,GAAG,GAAG,cAAc,EAAE,CAAC;;;;;;AAM7B,IAAAnD,aAAO,CACL,QAAO,MAAA,GAAG,KAAA,IAAA,IAAH,GAAG,KAAH,KAAA,CAAA,GAAA,KAAA,CAAA,GAAA,GAAG,CAAE,cAAc,0CAAE,SAAS,CAAA,KAAK,UAAU,EACpD,IAAI,EAEJ,+BAAA,oDAAA;AACE,QAAA,aAAa,EAAE,oCAAoC;AACpD,KAAA,CACF,CAAC;;AAGF,IAAAA,aAAO,CACL,QAAO,MAAA,GAAG,KAAA,IAAA,IAAH,GAAG,KAAH,KAAA,CAAA,GAAA,KAAA,CAAA,GAAA,GAAG,CAAE,SAAS,0CAAE,WAAW,CAAA,KAAK,WAAW,EAClD,IAAI,EAEJ,+BAAA,oDAAA;AACE,QAAA,aAAa,EAAE,0BAA0B;AAC1C,KAAA,CACF,CAAC;;IAGFA,aAAO,CACL,QAAO,CAAA,EAAA,GAAA,CAAA,EAAA,GAAA,MAAA,GAAG,KAAA,IAAA,IAAH,GAAG,KAAA,KAAA,CAAA,GAAA,KAAA,CAAA,GAAH,GAAG,CAAE,OAAO,MAAE,IAAA,IAAA,EAAA,KAAA,KAAA,CAAA,GAAA,KAAA,CAAA,GAAA,EAAA,CAAA,OAAO,MAAE,IAAA,IAAA,EAAA,KAAA,KAAA,CAAA,GAAA,KAAA,CAAA,GAAA,EAAA,CAAA,UAAU,MAAE,IAAA,IAAA,EAAA,KAAA,KAAA,CAAA,GAAA,KAAA,CAAA,GAAA,EAAA,CAAA,OAAO,CAAA,KAAK,UAAU,EAChE,IAAI,EAEJ,+BAAA,oDAAA;AACE,QAAA,aAAa,EAAE,2BAA2B;AAC3C,KAAA,CACF,CAAC;IACFA,aAAO,CACL,QAAO,CAAA,EAAA,GAAA,CAAA,EAAA,GAAA,MAAA,GAAG,KAAA,IAAA,IAAH,GAAG,KAAA,KAAA,CAAA,GAAA,KAAA,CAAA,GAAH,GAAG,CAAE,OAAO,MAAE,IAAA,IAAA,EAAA,KAAA,KAAA,CAAA,GAAA,KAAA,CAAA,GAAA,EAAA,CAAA,OAAO,MAAE,IAAA,IAAA,EAAA,KAAA,KAAA,CAAA,GAAA,KAAA,CAAA,GAAA,EAAA,CAAA,UAAU,MAAE,IAAA,IAAA,EAAA,KAAA,KAAA,CAAA,GAAA,KAAA,CAAA,GAAA,EAAA,CAAA,WAAW,CAAA,KAAK,UAAU,EACpE,IAAI,EAEJ,+BAAA,oDAAA;AACE,QAAA,aAAa,EAAE,2BAA2B;AAC3C,KAAA,CACF,CAAC;;IAGFA,aAAO,CACL,QAAO,CAAA,EAAA,GAAA,MAAA,GAAG,KAAA,IAAA,IAAH,GAAG,KAAH,KAAA,CAAA,GAAA,KAAA,CAAA,GAAA,GAAG,CAAE,OAAO,MAAA,IAAA,IAAA,EAAA,KAAA,KAAA,CAAA,GAAA,KAAA,CAAA,GAAA,EAAA,CAAE,YAAY,MAAE,IAAA,IAAA,EAAA,KAAA,KAAA,CAAA,GAAA,KAAA,CAAA,GAAA,EAAA,CAAA,IAAI,CAAA,KAAK,UAAU,EACtD,IAAI,EAEJ,+BAAA,oDAAA;AACE,QAAA,aAAa,EAAE,6BAA6B;AAC7C,KAAA,CACF,CAAC;AACJ,CAAC;AAED;;;;AAIG;AACH,SAAe,aAAa,CAAC,SAAiB,EAAA;;;;;;AACtC,oBAAA,KAAK,GAAG,mBAAmB,CAAC,SAAS,CAAC,CAAC;oBAMjC,OAAM,CAAA,CAAA,YAAA,MAAM,CAAC,MAAM,CAAC,MAAM,CAAC,SAAS,EAAE,KAAK,CAAC,CAAA,CAAA;;AAAlD,oBAAA,GAAG,GAAG,EAA4C,CAAA,IAAA,EAAA,CAAA;oBAClD,GAAG,GAAG,KAAK,CAAC,IAAI,CAAC,IAAI,UAAU,CAAC,GAAG,CAAC,CAAC,CAAC;AAC5C,oBAAA,OAAA,CAAA,CAAA,aAAO,GAAG,CAAC,GAAG,CAAC,UAAA,GAAG,EAAI,EAAA,OAAA,GAAG,CAAC,QAAQ,CAAC,EAAE,CAAC,CAAC,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC,CAAA,EAAA,CAAC,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC,CAAA;;;;AACnE,CAAA;AAED,SAAS,mBAAmB,CAAC,GAAW,EAAA;;;IAGtCO,iBAAW,CACT,cAAc,CAAC,IAAI,CAAC,GAAG,CAAC,EACxB,wCAAwC,CACzC,CAAC;AACF,IAAA,IAAI,OAAO,WAAW,KAAK,WAAW,EAAE;QACtC,OAAO,IAAI,WAAW,EAAE,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC;AACtC,KAAA;IAED,IAAM,IAAI,GAAG,IAAI,WAAW,CAAC,GAAG,CAAC,MAAM,CAAC,CAAC;AACzC,IAAA,IAAM,IAAI,GAAG,IAAI,UAAU,CAAC,IAAI,CAAC,CAAC;AAClC,IAAA,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,GAAG,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;QACnC,IAAI,CAAC,CAAC,CAAC,GAAG,GAAG,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC;AAC7B,KAAA;AACD,IAAA,OAAO,IAAI,CAAC;AACd;;AClTA;;;;;;;;;;;;;;;AAeG;AAgBH,IAAM,iBAAiB,GAAG,EAAE,CAAC;AAE7B;AACA,IAAA,uBAAA,kBAAA,UAAA,MAAA,EAAA;IAA6CjB,eAAgB,CAAA,uBAAA,EAAA,MAAA,CAAA,CAAA;AAA7D,IAAA,SAAA,uBAAA,GAAA;QAAA,IAgCC,KAAA,GAAA,MAAA,KAAA,IAAA,IAAA,MAAA,CAAA,KAAA,CAAA,IAAA,EAAA,SAAA,CAAA,IAAA,IAAA,CAAA;AA/BkB,QAAA,KAAA,CAAA,gBAAgB,GAAG,IAAI,GAAG,EAA0B,CAAC;AAE9D,QAAA,KAAA,CAAA,WAAW,GAAG,IAAI,OAAO,CAAO,UAAA,OAAO,EAAA;AAC7C,YAAA,KAAI,CAAC,kBAAkB,GAAG,OAAO,CAAC;AACpC,SAAC,CAAC,CAAC;;KA2BJ;IAzBC,uBAAkB,CAAA,SAAA,CAAA,kBAAA,GAAlB,UAAmB,EAA0B,EAAA;AAC3C,QAAA,IAAI,CAAC,gBAAgB,CAAC,GAAG,CAAC,EAAE,CAAC,CAAC;KAC/B,CAAA;IAED,uBAAqB,CAAA,SAAA,CAAA,qBAAA,GAArB,UAAsB,EAA0B,EAAA;AAC9C,QAAA,IAAI,CAAC,gBAAgB,CAAC,MAAM,CAAC,EAAE,CAAC,CAAC;KAClC,CAAA;;;AAID,IAAA,uBAAA,CAAA,SAAA,CAAA,aAAa,GAAb,YAAA;AACE,QAAA,IAAI,CAAC,mBAAmB,GAAG,IAAI,CAAC;AAChC,QAAA,IAAI,CAAC,2BAA2B,GAAG,KAAK,CAAC;KAC1C,CAAA;;IAGD,uBAAO,CAAA,SAAA,CAAA,OAAA,GAAP,UAAQ,KAAgB,EAAA;QACtB,IAAI,CAAC,kBAAkB,EAAE,CAAC;AAC1B,QAAA,IAAI,CAAC,gBAAgB,CAAC,OAAO,CAAC,UAAA,EAAE,EAAA,EAAI,OAAA,EAAE,CAAC,KAAK,CAAC,CAAT,EAAS,CAAC,CAAC;AAC/C,QAAA,OAAO,MAAM,CAAA,SAAA,CAAA,OAAO,CAAC,IAAA,CAAA,IAAA,EAAA,KAAK,CAAC,CAAC;KAC7B,CAAA;AAEK,IAAA,uBAAA,CAAA,SAAA,CAAA,WAAW,GAAjB,YAAA;;;;4BACE,OAAM,CAAA,CAAA,YAAA,IAAI,CAAC,WAAW,CAAA,CAAA;;AAAtB,wBAAA,EAAA,CAAA,IAAA,EAAsB,CAAC;;;;;AACxB,KAAA,CAAA;IACH,OAAC,uBAAA,CAAA;AAAD,CAhCA,CAA6C,gBAAgB,CAgC5D,CAAA,CAAA;AAED;;AAEG;SACa,iBAAiB,CAC/B,IAAkB,EAClB,IAAmB,EACnB,OAA6B,EAAA;AAA7B,IAAA,IAAA,OAAA,KAAA,KAAA,CAAA,EAAA,EAAA,OAA6B,GAAA,IAAA,CAAA,EAAA;IAE7B,OAAO;AACL,QAAA,IAAI,EAAA,IAAA;AACJ,QAAA,OAAO,EAAA,OAAA;AACP,QAAA,WAAW,EAAE,IAAI;QACjB,SAAS,EAAE,iBAAiB,EAAE;AAC9B,QAAA,QAAQ,EAAE,IAAI;QACd,QAAQ,EAAE,IAAI,CAAC,QAAQ;AACvB,QAAA,KAAK,EAAEoB,kBAAY,CAAC,IAAI,EAA8B,eAAA,mCAAA;KACvD,CAAC;AACJ,CAAC;AAEe,SAAA,iBAAiB,CAC/B,IAAkB,EAClB,KAAgB,EAAA;AAEhB,IAAA,OAAO,OAAO,EAAE,CAAC,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,EAAE,KAAgC,CAAC,CAAC;AAChF,CAAC;AAEK,SAAgB,kBAAkB,CACtC,IAAkB,EAAA;;;;;wBAEH,OAAM,CAAA,CAAA,YAAA,OAAO,EAAE,CAAC,IAAI,CACjC,cAAc,CAAC,IAAI,CAAC,CACrB,CAAA,CAAA;;oBAFK,KAAK,IAAI,EAAA,CAAA,IAAA,EAEd,CAAqB,CAAA;AAClB,oBAAA,IAAA,CAAA,KAAK,EAAL,OAAK,CAAA,CAAA,YAAA,CAAA,CAAA,CAAA;oBACP,OAAM,CAAA,CAAA,YAAA,OAAO,EAAE,CAAC,OAAO,CAAC,cAAc,CAAC,IAAI,CAAC,CAAC,CAAA,CAAA;;AAA7C,oBAAA,EAAA,CAAA,IAAA,EAA6C,CAAC;;AAEhD,gBAAA,KAAA,CAAA,EAAA,OAAA,CAAA,CAAA,aAAO,KAAK,CAAC,CAAA;;;;AACd,CAAA;AAEe,SAAA,uBAAuB,CACrC,YAAuB,EACvB,GAAW,EAAA;;;AAGX,IAAA,IAAM,WAAW,GAAG,wBAAwB,CAAC,GAAG,CAAC,CAAC;;;;;;AAMlD,IAAA,IAAI,WAAW,CAAC,QAAQ,CAAC,mBAAmB,CAAC,EAAE;;;;AAI7C,QAAA,IAAM,MAAM,GAAG,mBAAmB,CAAC,WAAW,CAAC,CAAC;;AAEhD,QAAA,IAAM,WAAW,GAAG,MAAM,CAAC,eAAe,CAAC;cACvC,eAAe,CAAC,kBAAkB,CAAC,MAAM,CAAC,eAAe,CAAC,CAAC,CAAC;cAC5D,IAAI,CAAC;QACT,IAAM,IAAI,GAAG,CAAA,EAAA,GAAA,CAAA,EAAA,GAAA,WAAW,KAAX,IAAA,IAAA,WAAW,uBAAX,WAAW,CAAG,MAAM,CAAC,MAAA,IAAA,IAAA,EAAA,KAAA,KAAA,CAAA,GAAA,KAAA,CAAA,GAAA,EAAA,CAAE,KAAK,CAAC,OAAO,CAAC,MAAG,IAAA,IAAA,EAAA,KAAA,KAAA,CAAA,GAAA,KAAA,CAAA,GAAA,EAAA,CAAA,CAAC,CAAC,CAAC;AACxD,QAAA,IAAM,KAAK,GAAG,IAAI,GAAGA,kBAAY,CAAC,IAAI,CAAC,GAAG,IAAI,CAAC;AAC/C,QAAA,IAAI,KAAK,EAAE;YACT,OAAO;gBACL,IAAI,EAAE,YAAY,CAAC,IAAI;gBACvB,OAAO,EAAE,YAAY,CAAC,OAAO;gBAC7B,QAAQ,EAAE,YAAY,CAAC,QAAQ;AAC/B,gBAAA,KAAK,EAAA,KAAA;AACL,gBAAA,WAAW,EAAE,IAAI;AACjB,gBAAA,SAAS,EAAE,IAAI;AACf,gBAAA,QAAQ,EAAE,IAAI;aACf,CAAC;AACH,SAAA;AAAM,aAAA;YACL,OAAO;gBACL,IAAI,EAAE,YAAY,CAAC,IAAI;gBACvB,OAAO,EAAE,YAAY,CAAC,OAAO;gBAC7B,QAAQ,EAAE,YAAY,CAAC,QAAQ;gBAC/B,SAAS,EAAE,YAAY,CAAC,SAAS;AACjC,gBAAA,WAAW,EAAE,WAAW;AACxB,gBAAA,QAAQ,EAAE,IAAI;aACf,CAAC;AACH,SAAA;AACF,KAAA;AAED,IAAA,OAAO,IAAI,CAAC;AACd,CAAC;AAED,SAAS,iBAAiB,GAAA;IACxB,IAAM,KAAK,GAAG,EAAE,CAAC;IACjB,IAAM,YAAY,GAChB,gEAAgE,CAAC;IACnE,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,iBAAiB,EAAE,CAAC,EAAE,EAAE;AAC1C,QAAA,IAAM,GAAG,GAAG,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,MAAM,EAAE,GAAG,YAAY,CAAC,MAAM,CAAC,CAAC;QAC5D,KAAK,CAAC,IAAI,CAAC,YAAY,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,CAAC;AACtC,KAAA;AACD,IAAA,OAAO,KAAK,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;AACxB,CAAC;AAED,SAAS,OAAO,GAAA;AACd,IAAA,OAAOX,kBAAY,CAAC,uBAAuB,CAAC,CAAC;AAC/C,CAAC;AAED,SAAS,cAAc,CAAC,IAAkB,EAAA;AACxC,IAAA,OAAOgB,yBAAmB,CAAA,WAAA,2BAAqB,IAAI,CAAC,MAAM,CAAC,MAAM,EAAE,IAAI,CAAC,IAAI,CAAC,CAAC;AAChF,CAAC;AAED,SAAS,eAAe,CAAC,IAAY,EAAA;IACnC,IAAI;AACF,QAAA,OAAO,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC;AACzB,KAAA;AAAC,IAAA,OAAO,CAAC,EAAE;AACV,QAAA,OAAO,IAAI,CAAC;AACb,KAAA;AACH,CAAC;AAED;AACM,SAAU,wBAAwB,CAAC,GAAW,EAAA;AAClD,IAAA,IAAM,MAAM,GAAG,mBAAmB,CAAC,GAAG,CAAC,CAAC;IACxC,IAAM,IAAI,GAAG,MAAM,CAAC,MAAM,CAAC,GAAG,kBAAkB,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC,GAAG,SAAS,CAAC;;IAE7E,IAAM,cAAc,GAAG,mBAAmB,CAAC,IAAI,CAAC,CAAC,MAAM,CAAC,CAAC;;AAEzD,IAAA,IAAM,WAAW,GAAG,MAAM,CAAC,cAAc,CAAC;AACxC,UAAE,kBAAkB,CAAC,MAAM,CAAC,cAAc,CAAC,CAAC;UAC1C,SAAS,CAAC;IACd,IAAM,iBAAiB,GAAG,mBAAmB,CAAC,WAAW,CAAC,CAAC,MAAM,CAAC,CAAC;IACnE,OAAO,iBAAiB,IAAI,WAAW,IAAI,cAAc,IAAI,IAAI,IAAI,GAAG,CAAC;AAC3E,CAAC;AAED;;;AAGG;AACH,SAAS,mBAAmB,CAAC,GAAuB,EAAA;AAClD,IAAA,IAAI,EAAC,GAAG,KAAA,IAAA,IAAH,GAAG,KAAA,KAAA,CAAA,GAAA,KAAA,CAAA,GAAH,GAAG,CAAE,QAAQ,CAAC,GAAG,CAAC,CAAA,EAAE;AACvB,QAAA,OAAO,EAAE,CAAC;AACX,KAAA;AAEK,IAAA,IAAA,EAAe,GAAA,GAAG,CAAC,KAAK,CAAC,GAAG,CAAC,CAAA,CAA3B,EAAA,CAAA,CAAA,CAAA,CAAK,KAAA,IAAI,eAAmB;IACpC,OAAOsC,sBAAiB,CAAC,IAAI,CAAC,IAAI,CAAC,GAAG,CAAC,CAA2B,CAAC;AACrE;;AC7MA;;;;;;;;;;;;;;;AAeG;AAmCH;;;AAGG;AACH,IAAM,wBAAwB,GAAG,GAAG,CAAC;AAErC,IAAA,4BAAA,kBAAA,YAAA;AAAA,IAAA,SAAA,4BAAA,GAAA;QACW,IAAoB,CAAA,oBAAA,GAAG,yBAAyB,CAAC;AACjD,QAAA,IAAA,CAAA,sBAAsB,GAAG,IAAI,CAAC;AACtB,QAAA,IAAA,CAAA,aAAa,GAAG,IAAI,GAAG,EAAmC,CAAC;QAC3D,IAAwB,CAAA,wBAAA,GAAkC,EAAE,CAAC;QAE9E,IAAmB,CAAA,mBAAA,GAAG,kBAAkB,CAAC;QACzC,IAAuB,CAAA,uBAAA,GAAG,uBAAuB,CAAC;KAwHnD;IAtHO,4BAAW,CAAA,SAAA,CAAA,WAAA,GAAjB,UAAkB,IAAkB,EAAA;;;;AAC5B,gBAAA,GAAG,GAAG,IAAI,CAAC,IAAI,EAAE,CAAC;gBACpB,OAAO,GAAG,IAAI,CAAC,aAAa,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;gBAC1C,IAAI,CAAC,OAAO,EAAE;AACZ,oBAAA,OAAO,GAAG,IAAI,uBAAuB,CAAC,IAAI,CAAC,CAAC;oBAC5C,IAAI,CAAC,aAAa,CAAC,GAAG,CAAC,GAAG,EAAE,OAAO,CAAC,CAAC;AACrC,oBAAA,IAAI,CAAC,uBAAuB,CAAC,IAAI,EAAE,OAAO,CAAC,CAAC;AAC7C,iBAAA;AACD,gBAAA,OAAA,CAAA,CAAA,aAAO,OAAO,CAAC,CAAA;;;AAChB,KAAA,CAAA;IAED,4BAAU,CAAA,SAAA,CAAA,UAAA,GAAV,UAAW,IAAkB,EAAA;QAC3B/C,WAAK,CAAC,IAAI,EAAA,6CAAA,6CAAwC,CAAC;KACpD,CAAA;IAEK,4BAAa,CAAA,SAAA,CAAA,aAAA,GAAnB,UACE,IAAkB,EAClB,QAAsB,EACtB,QAAuB,EACvB,OAAgB,EAAA;;;;;;wBAEhB,0BAA0B,CAAC,IAAI,CAAC,CAAC;AACjB,wBAAA,OAAA,CAAA,CAAA,YAAM,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,CAAA,CAAA;;AAAtC,wBAAA,OAAO,GAAG,EAA4B,CAAA,IAAA,EAAA,CAAA;AAC5C,wBAAA,OAAA,CAAA,CAAA,YAAM,OAAO,CAAC,WAAW,EAAE,CAAA,CAAA;;AAA3B,wBAAA,EAAA,CAAA,IAAA,EAA2B,CAAC;;;;wBAK5B,OAAO,CAAC,aAAa,EAAE,CAAC;AACxB,wBAAA,sBAAsB,EAAE,CAAC;AAEzB,wBAAA,OAAA,CAAA,CAAA,YAAM,IAAI,CAAC,iBAAiB,CAAC,IAAI,CAAC,CAAA,CAAA;;AAAlC,wBAAA,EAAA,CAAA,IAAA,EAAkC,CAAC;wBAE7B,KAAK,GAAG,iBAAiB,CAAC,IAAI,EAAE,QAAQ,EAAE,OAAO,CAAC,CAAC;AACzD,wBAAA,OAAA,CAAA,CAAA,YAAM,iBAAiB,CAAC,IAAI,EAAE,KAAK,CAAC,CAAA,CAAA;;AAApC,wBAAA,EAAA,CAAA,IAAA,EAAoC,CAAC;wBACzB,OAAM,CAAA,CAAA,YAAA,mBAAmB,CAAC,IAAI,EAAE,KAAK,EAAE,QAAQ,CAAC,CAAA,CAAA;;AAAtD,wBAAA,GAAG,GAAG,EAAgD,CAAA,IAAA,EAAA,CAAA;AAC7C,wBAAA,OAAA,CAAA,CAAA,YAAM,gBAAgB,CAAC,GAAG,CAAC,CAAA,CAAA;;AAApC,wBAAA,MAAM,GAAG,EAA2B,CAAA,IAAA,EAAA,CAAA;wBAC1C,OAAO,CAAA,CAAA,aAAA,iBAAiB,CAAC,IAAI,EAAE,OAAO,EAAE,MAAM,CAAC,CAAC,CAAA;;;;AACjD,KAAA,CAAA;AAED,IAAA,4BAAA,CAAA,SAAA,CAAA,4BAA4B,GAA5B,UACE,KAAmB,EACnB,GAAkC,EAAA;AAElC,QAAA,MAAM,IAAI,KAAK,CAAC,yBAAyB,CAAC,CAAC;KAC5C,CAAA;IAED,4BAAiB,CAAA,SAAA,CAAA,iBAAA,GAAjB,UAAkB,IAAkB,EAAA;AAClC,QAAA,IAAM,GAAG,GAAG,IAAI,CAAC,IAAI,EAAE,CAAC;AACxB,QAAA,IAAI,CAAC,IAAI,CAAC,wBAAwB,CAAC,GAAG,CAAC,EAAE;YACvC,IAAI,CAAC,wBAAwB,CAAC,GAAG,CAAC,GAAG,eAAe,CAAC,IAAI,CAAC,CAAC;AAC5D,SAAA;AAED,QAAA,OAAO,IAAI,CAAC,wBAAwB,CAAC,GAAG,CAAC,CAAC;KAC3C,CAAA;AAEO,IAAA,4BAAA,CAAA,SAAA,CAAA,uBAAuB,GAA/B,UACE,IAAkB,EAClB,OAAyB,EAAA;QAF3B,IA6DC,KAAA,GAAA,IAAA,CAAA;;AAxDO,QAAA,IAAA,EAA+C,GAAA,cAAc,EAAE,EAA7D,cAAc,GAAA,EAAA,CAAA,cAAA,EAAE,aAAa,GAAA,EAAA,CAAA,aAAA,EAAE,SAAS,GAAA,EAAA,CAAA,SAAqB,CAAC;QAEtE,IAAM,cAAc,GAAG,UAAU,CAAC,YAAA,EAAA,OAAAb,eAAA,CAAA,KAAA,EAAA,KAAA,CAAA,EAAA,KAAA,CAAA,EAAA,YAAA;;;;;;AAGhC,oBAAA,OAAA,CAAA,CAAA,YAAM,kBAAkB,CAAC,IAAI,CAAC,CAAA,CAAA;;;;AAA9B,wBAAA,EAAA,CAAA,IAAA,EAA8B,CAAC;AAC/B,wBAAA,OAAO,CAAC,OAAO,CAAC,eAAe,EAAE,CAAC,CAAC;;;;aACpC,EAAE,wBAAwB,CAAC,CAAC;QAE7B,IAAM,gBAAgB,GAAG,UACvB,SAAwC,EAAA,EAAA,OAAAA,eAAA,CAAA,KAAA,EAAA,KAAA,CAAA,EAAA,KAAA,CAAA,EAAA,YAAA;;;;;;wBAGxC,YAAY,CAAC,cAAc,CAAC,CAAC;AAER,wBAAA,OAAA,CAAA,CAAA,YAAM,kBAAkB,CAAC,IAAI,CAAC,CAAA,CAAA;;AAA7C,wBAAA,YAAY,GAAG,EAA8B,CAAA,IAAA,EAAA,CAAA;wBAC/C,UAAU,GAAqB,IAAI,CAAC;wBACxC,IAAI,YAAY,KAAI,SAAS,KAAT,IAAA,IAAA,SAAS,KAAT,KAAA,CAAA,GAAA,KAAA,CAAA,GAAA,SAAS,CAAG,KAAK,CAAC,CAAA,EAAE;4BACtC,UAAU,GAAG,uBAAuB,CAAC,YAAY,EAAE,SAAS,CAAC,KAAK,CAAC,CAAC,CAAC;AACtE,yBAAA;;wBAGD,OAAO,CAAC,OAAO,CAAC,UAAU,IAAI,eAAe,EAAE,CAAC,CAAC;;;;aAClD,CAAC;;QAGF,IACE,OAAO,cAAc,KAAK,WAAW;AACrC,YAAA,OAAO,cAAc,CAAC,SAAS,KAAK,UAAU,EAC9C;AACA,YAAA,cAAc,CAAC,SAAS,CAAC,IAAI,EAAE,gBAAgB,CAAC,CAAC;AAClD,SAAA;;;;;;QAOD,IAAM,qBAAqB,GAAG,aAAa,CAAC;QAC5C,IAAM,aAAa,GAAG,EAAA,CAAA,MAAA,CAAG,SAAS,CAAC,WAAW,CAAC,WAAW,EAAE,EAAA,KAAA,CAAK,CAAC;AAClE,QAAA,cAAc,EAAE,CAAC,aAAa,GAAG,UAAM,GAAG,EAAA,EAAA,OAAAA,eAAA,CAAA,KAAA,EAAA,KAAA,CAAA,EAAA,KAAA,CAAA,EAAA,YAAA;;gBACxC,IAAI,GAAG,CAAC,WAAW,EAAE,CAAC,UAAU,CAAC,aAAa,CAAC,EAAE;;;AAG/C,oBAAA,gBAAgB,CAAC,EAAE,GAAG,EAAA,GAAA,EAAE,CAAC,CAAC;AAC3B,iBAAA;;AAED,gBAAA,IAAI,OAAO,qBAAqB,KAAK,UAAU,EAAE;oBAC/C,IAAI;wBACF,qBAAqB,CAAC,GAAG,CAAC,CAAC;AAC5B,qBAAA;AAAC,oBAAA,OAAO,CAAC,EAAE;;AAEV,wBAAA,OAAO,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC;AAClB,qBAAA;AACF,iBAAA;;;aACF,CAAC;KACH,CAAA;IACH,OAAC,4BAAA,CAAA;AAAD,CAAC,EAAA,CAAA,CAAA;AAED;;;;;AAKG;AACI,IAAM,4BAA4B,GACvC,6BAA6B;AAE/B,SAAS,eAAe,GAAA;IACtB,OAAO;AACL,QAAA,IAAI,EAAuB,SAAA;AAC3B,QAAA,OAAO,EAAE,IAAI;AACb,QAAA,SAAS,EAAE,IAAI;AACf,QAAA,WAAW,EAAE,IAAI;AACjB,QAAA,QAAQ,EAAE,IAAI;AACd,QAAA,QAAQ,EAAE,IAAI;QACd,KAAK,EAAEiB,kBAAY,CAA6B,eAAA,mCAAA;KACjD,CAAC;AACJ;;AC5MA;;;;;;;;;;;;;;;AAeG;AAqCH;AACA;AACA;AACgB,SAAA,sBAAsB,CAAC,IAAU,EAAE,SAAiB,EAAA;IAClEC,eAAS,CAAC,IAAI,CAAC,CAAC,aAAa,CAAC,SAAS,CAAC,CAAC;AAC3C;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;"}