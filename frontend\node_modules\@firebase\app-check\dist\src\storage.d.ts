/**
 * @license
 * Copyright 2020 Google LLC
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *   http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
import { FirebaseApp } from '@firebase/app';
import { AppCheckTokenInternal } from './types';
/**
 * Always resolves. In case of an error reading from indexeddb, resolve with undefined
 */
export declare function readTokenFromStorage(app: FirebaseApp): Promise<AppCheckTokenInternal | undefined>;
/**
 * Always resolves. In case of an error writing to indexeddb, print a warning and resolve the promise
 */
export declare function writeTokenToStorage(app: FirebaseApp, token?: AppCheckTokenInternal): Promise<void>;
export declare function readOrCreateDebugTokenFromStorage(): Promise<string>;
