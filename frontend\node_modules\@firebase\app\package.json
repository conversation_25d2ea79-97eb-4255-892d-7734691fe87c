{"name": "@firebase/app", "version": "0.10.13", "description": "The primary entrypoint to the Firebase JS SDK", "author": "Firebase <<EMAIL>> (https://firebase.google.com/)", "main": "dist/index.cjs.js", "browser": "dist/esm/index.esm2017.js", "module": "dist/esm/index.esm2017.js", "esm5": "dist/esm/index.esm5.js", "exports": {".": {"types": "./dist/app-public.d.ts", "require": "./dist/index.cjs.js", "esm5": "./dist/esm/index.esm5.js", "default": "./dist/esm/index.esm2017.js"}, "./package.json": "./package.json"}, "files": ["dist"], "scripts": {"lint": "eslint -c .eslintrc.js '**/*.ts' --ignore-path '../../.gitignore'", "lint:fix": "eslint --fix -c .eslintrc.js '**/*.ts' --ignore-path '../../.gitignore'", "build": "rollup -c && yarn api-report", "build:release": "rollup -c rollup.config.release.js && yarn api-report && yarn typings:public", "build:deps": "lerna run --scope @firebase/app --include-dependencies build", "dev": "rollup -c -w", "test": "run-p --npm-path npm lint test:all", "test:ci": "node ../../scripts/run_tests_in_ci.js -s test:all", "test:all": "run-p --npm-path npm test:browser test:node", "test:browser": "karma start", "test:node": "TS_NODE_COMPILER_OPTIONS='{\"module\":\"commonjs\"}' nyc --reporter lcovonly -- mocha src/**/*.test.ts --config ../../config/mocharc.node.js", "trusted-type-check": "tsec -p tsconfig.json --noEmit", "api-report": "api-extractor run --local --verbose", "doc": "api-documenter markdown --input temp --output docs", "build:doc": "yarn build && yarn doc", "typings:public": "node ../../scripts/build/use_typings.js ./dist/app-public.d.ts", "typings:internal": "node ../../scripts/build/use_typings.js ./dist/app.d.ts"}, "dependencies": {"@firebase/util": "1.10.0", "@firebase/logger": "0.4.2", "@firebase/component": "0.6.9", "idb": "7.1.1", "tslib": "^2.1.0"}, "license": "Apache-2.0", "devDependencies": {"@rollup/plugin-json": "4.1.0", "rollup": "2.79.1", "rollup-plugin-replace": "2.2.0", "rollup-plugin-typescript2": "0.31.2", "rollup-plugin-dts": "5.3.1", "typescript": "4.7.4"}, "repository": {"directory": "packages/app", "type": "git", "url": "git+https://github.com/firebase/firebase-js-sdk.git"}, "bugs": {"url": "https://github.com/firebase/firebase-js-sdk/issues"}, "typings": "./dist/app-public.d.ts", "nyc": {"extension": [".ts"], "reportDir": "./coverage/node"}}